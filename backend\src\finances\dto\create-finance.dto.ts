import { IsNotEmpty, IsString, IsOptional, IsEnum, IsDateString, IsNumber, IsBoolean, IsDecimal } from 'class-validator';

export class CreateFinanceDto {
  @IsNotEmpty()
  @IsEnum(['income', 'expense'])
  transaction_type: 'income' | 'expense';

  @IsOptional()
  @IsNumber()
  category_id?: number;

  @IsOptional()
  @IsBoolean()
  is_saving?: boolean;

  @IsOptional()
  @IsString()
  description?: string;

  @IsNotEmpty()
  @IsString()
  amount: string; // DECIMAL como string

  @IsNotEmpty()
  @IsDateString()
  transaction_date: string;
}
