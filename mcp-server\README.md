# Dupli MCP Server

MCP Server para integração com o sistema Dupli AgentWPP através do N8N.

## 🚀 Instalação

```bash
cd mcp-server
npm install
```

## ⚙️ Configuração

### Variáveis de Ambiente

Crie um arquivo `.env` na raiz do projeto:

```env
API_BASE_URL=http://localhost:3000
N8N_API_KEY=your-secret-api-key-here
```

### Build do Projeto

```bash
npm run build
```

## 🔧 Uso

### Executar em Desenvolvimento

```bash
npm run dev
```

### Executar em Produção

```bash
npm start
```

## 📋 Tools Disponíveis

### 🔗 Integração
- `check_integration`: Verificar integração WhatsApp
- `get_dashboard`: Obter dashboard do usuário

### 📋 Tarefas
- `create_task`: Criar nova tarefa
- `list_tasks`: Listar tarefas (padrão: 10 registros) com filtros:
  - `search`: Buscar no nome/descrição
  - `start_date/end_date`: Filtrar por período
  - `completed`: Filtrar por status
  - `category_id`: Filtrar por categoria
- `get_task`: Obter tarefa específica
- `update_task`: Atualizar tarefa
- `delete_task`: Remover tarefa
- `complete_task`: Marcar como concluída
- `list_task_categories`: Listar categorias
- `create_task_category`: Criar categoria

### 💰 Finanças
- `create_finance`: Criar nova transação
- `list_finances`: Listar transações (padrão: 10 registros) com filtros:
  - `search`: Buscar na descrição
  - `start_date/end_date`: Filtrar por período
  - `transaction_type`: Filtrar por tipo (income/expense)
  - `category_id`: Filtrar por categoria
  - `is_saving`: Filtrar poupanças
- `get_finance_summary`: Obter resumo financeiro
- `list_finance_categories`: Listar categorias

### 💡 Ideias
- `create_idea`: Criar nova ideia
- `list_ideas`: Listar ideias (padrão: 10 registros) com filtros:
  - `search`: Buscar no nome/descrição/conteúdo
  - `start_date/end_date`: Filtrar por período
  - `category_id`: Filtrar por categoria
  - `is_favorite`: Filtrar favoritas
- `toggle_idea_favorite`: Alternar favorito
- `list_idea_categories`: Listar categorias

## 🔌 Configuração no N8N

### ⚡ Método SSE (Server-Sent Events)

1. **Inicie o servidor MCP:**
   ```bash
   npm start
   ```
   O servidor estará rodando em `http://localhost:3001/mcp`

2. **Configure no N8N:**
   - **Tipo**: SSE Endpoint
   - **URL**: `http://localhost:3001/mcp`
   - **Variáveis de ambiente**:
     ```json
     {
       "API_BASE_URL": "http://localhost:3000",
       "N8N_API_KEY": "your-secret-api-key-here"
     }
     ```

### 🔧 Configuração Personalizada

Para alterar a porta ou outras configurações:

```bash
# Alterar porta
PORT=3002 npm start

# Configurar variáveis de ambiente
API_BASE_URL=https://sua-api.com N8N_API_KEY=sua-chave npm start
```

## 📝 Exemplos de Uso

### Verificar Integração

```json
{
  "tool": "check_integration",
  "args": {
    "phone": "5511999999999"
  }
}
```

### Criar Tarefa

```json
{
  "tool": "create_task",
  "args": {
    "phone": "5511999999999",
    "task_type": "task",
    "name": "Reunião importante",
    "description": "Reunião com cliente às 14h",
    "task_date": "2025-07-15T14:00:00.000Z"
  }
}
```

### Listar Tarefas com Filtros

```json
{
  "tool": "list_tasks",
  "args": {
    "phone": "5511999999999",
    "search": "reunião",
    "start_date": "2025-07-01T00:00:00.000Z",
    "end_date": "2025-07-31T23:59:59.999Z",
    "completed": false,
    "limit": 10
  }
}
```

### Listar Finanças do Mês

```json
{
  "tool": "list_finances",
  "args": {
    "phone": "5511999999999",
    "transaction_type": "expense",
    "start_date": "2025-07-01T00:00:00.000Z",
    "end_date": "2025-07-31T23:59:59.999Z"
  }
}
```

### Buscar Ideias Favoritas

```json
{
  "tool": "list_ideas",
  "args": {
    "phone": "5511999999999",
    "is_favorite": true,
    "search": "app"
  }
}
```

## 🛠️ Desenvolvimento

### Estrutura do Projeto

```
mcp-server/
├── src/
│   └── index.ts           # Servidor MCP completo
├── dist/                  # Arquivos compilados
├── package.json
├── tsconfig.json
└── README.md
```

## 🔍 Logs e Debug

O servidor MCP gera logs no stderr:

```bash
npm run dev 2> debug.log
```

## 🚨 Troubleshooting

### Erro de Conexão com API

- Verifique se a API está rodando
- Confirme a URL base e API key
- Verifique se não há problemas de firewall

### Erro no N8N

- Confirme se o caminho para o script está correto
- Verifique se as variáveis de ambiente estão configuradas
- Confirme se o Node.js está instalado

## 📞 Suporte

Para suporte, consulte a documentação da API em `backend/AGENTWPP_API.md`.

---

## 🆕 Novidades v1.0.0

### ✨ Filtros Avançados
- **Busca por texto**: Pesquisar em nome, descrição e conteúdo
- **Filtros por data**: Período específico com `start_date` e `end_date`
- **Filtros por categoria**: Filtrar por `category_id`
- **Filtros específicos**: Status, tipo de transação, favoritos, etc.

### 📊 Listagem Otimizada
- **Padrão**: 10 registros por página (otimizado para conversas)
- **Configurável**: Possibilidade de aumentar o `limit` quando necessário
- **Paginação**: Suporte completo a paginação

### 🔍 Busca Inteligente
- **Tasks**: Busca em nome e descrição
- **Finances**: Busca em descrição
- **Ideas**: Busca em nome, descrição e conteúdo
