"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DevController = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const dev_service_1 = require("./dev.service");
let DevController = class DevController {
    jwtService;
    devService;
    constructor(jwtService, devService) {
        this.jwtService = jwtService;
        this.devService = devService;
    }
    async generateDevToken(body) {
        if (process.env.NODE_ENV !== 'development' || process.env.DEV_MODE_ENABLED !== 'true') {
            throw new common_1.HttpException('Endpoint disponível apenas em modo de desenvolvimento', common_1.HttpStatus.FORBIDDEN);
        }
        const userId = body.userId || 1;
        const email = body.email || '<EMAIL>';
        const payload = { sub: userId, email };
        const access_token = this.jwtService.sign(payload);
        return {
            access_token,
            user: {
                id: userId,
                email,
                name: 'Dev User',
                timezone: 'America/Sao_Paulo'
            },
            message: 'Token de desenvolvimento gerado com sucesso'
        };
    }
    async getOrCreateTestUser() {
        if (process.env.NODE_ENV !== 'development' || process.env.DEV_MODE_ENABLED !== 'true') {
            throw new common_1.HttpException('Endpoint disponível apenas em modo de desenvolvimento', common_1.HttpStatus.FORBIDDEN);
        }
        return await this.devService.getOrCreateTestUser();
    }
    async seedTestData(body) {
        if (process.env.NODE_ENV !== 'development' || process.env.DEV_MODE_ENABLED !== 'true') {
            throw new common_1.HttpException('Endpoint disponível apenas em modo de desenvolvimento', common_1.HttpStatus.FORBIDDEN);
        }
        const userId = body.userId || 1;
        return await this.devService.seedTestData(userId);
    }
};
exports.DevController = DevController;
__decorate([
    (0, common_1.Post)('generate-token'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DevController.prototype, "generateDevToken", null);
__decorate([
    (0, common_1.Get)('test-user'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DevController.prototype, "getOrCreateTestUser", null);
__decorate([
    (0, common_1.Post)('seed-data'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DevController.prototype, "seedTestData", null);
exports.DevController = DevController = __decorate([
    (0, common_1.Controller)('dev'),
    __metadata("design:paramtypes", [jwt_1.JwtService,
        dev_service_1.DevService])
], DevController);
//# sourceMappingURL=dev.controller.js.map