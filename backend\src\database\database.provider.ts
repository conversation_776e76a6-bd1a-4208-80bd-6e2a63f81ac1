import { Kysely, MysqlDialect } from 'kysely';
import { createPool } from 'mariadb';
import { ConfigService } from '@nestjs/config';
import { Database } from '../database.types';

export const DATABASE_CONNECTION = 'DATABASE_CONNECTION';

export const databaseProviders = [
  {
    provide: DATABASE_CONNECTION,
    useFactory: (configService: ConfigService): Kysely<Database> => {
      const dialect = new MysqlDialect({
        pool: createPool({
          host: configService.get('DB_HOST') || 'localhost',
          port: configService.get('DB_PORT') || 3306,
          user: configService.get('DB_USER') || 'root',
          password: configService.get('DB_PASSWORD'),
          database: configService.get('DB_DATABASE'),
          connectionLimit: 10,
        }),
      });

      return new Kysely<Database>({
        dialect,
      });
    },
    inject: [ConfigService],
  },
];
