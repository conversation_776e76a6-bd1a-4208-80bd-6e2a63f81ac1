import React, { useState } from 'react';
import { TrendingUp, TrendingDown, Wallet, ChevronRight, Plus, Edit2, <PERSON>gyBank, AlertCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import <PERSON><PERSON><PERSON> from '../components/PieChart';
import Header from '../components/Header';
import AnnualOverviewCard from '../components/AnnualOverviewCard';
import AddTransactionModal from '../components/AddTransactionModal';
import EditBudgetModal from '../components/EditBudgetModal';
import { useFinances, useFinancesSummary, useFinanceCategories } from '../hooks/useFinances';

const FinancesPage: React.FC = () => {
  const navigate = useNavigate();
  const [isAddTransactionModalOpen, setIsAddTransactionModalOpen] = useState(false);
  const [isEditBudgetModalOpen, setIsEditBudgetModalOpen] = useState(false);

  // Fetch data from API
  const { data: financesData, isLoading: financesLoading, error: financesError } = useFinances();
  const { data: summaryData, isLoading: summaryLoading } = useFinancesSummary();
  const { data: categoriesData } = useFinanceCategories();

  const isLoading = financesLoading || summaryLoading;

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando dados financeiros...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (financesError) {
    return (
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertCircle className="h-8 w-8 text-red-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Erro ao carregar dados</h2>
          <p className="text-gray-600 mb-4">
            Não foi possível carregar os dados financeiros. Tente novamente.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }

  const handleBudgetUpdate = (newBudget: number) => {
    // TODO: Implement budget update API call
    console.log('Update budget:', newBudget);
  };

  const handleAddTransaction = (transaction: {
    type: 'income' | 'expense' | 'savings';
    amount: number;
    category: string;
    description: string;
  }) => {
    // TODO: Implement add transaction API call
    console.log('Adding transaction:', transaction);
  };

  // Use real data from API or fallback to defaults
  const summary = summaryData || {
    totalIncome: 0,
    totalExpenses: 0,
    totalSavings: 0,
    balance: 0
  };

  // Calculate budget (for now, use a default or get from user config)
  const budget = 5000; // TODO: Get from user config

  // Reorganized quadrant data with real API data
  const quadrantData = [
    {
      title: 'Cofrinho',
      value: summary.totalSavings,
      trend: { value: 15, label: 'este mês' },
      icon: PiggyBank,
      route: '/finances/cofrinho'
    },
    {
      title: 'Receita',
      value: summary.totalIncome,
      trend: { value: 8, label: 'vs mês anterior' },
      icon: TrendingUp,
      route: '/finances/income'
    },
    {
      title: 'Despesas',
      value: summary.totalExpenses,
      trend: { value: -3, label: 'vs mês anterior' },
      icon: TrendingDown,
      route: '/finances/expenses'
    },
    {
      title: 'Disponível',
      value: summary.balance,
      trend: { value: 12, label: 'este mês' },
      icon: Wallet,
      route: '/finances/overview'
    }
  ];

  return (
    <div className="relative min-h-screen bg-[#F7F7F7]">
      <Header 
        onAddClick={() => setIsAddTransactionModalOpen(true)}
        onEditBudgetClick={() => setIsEditBudgetModalOpen(true)}
      />

      <div className="max-w-4xl mx-auto pt-20 px-4 pb-24 md:pb-6 space-y-6">
        {/* Financial quadrant */}
        <div className="grid grid-cols-2 gap-4">
          {quadrantData.map((item, index) => {
            const Icon = item.icon;
            const isCofrinho = item.title === 'Cofrinho';
            return (
              <motion.div
                key={item.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                onClick={() => item.route !== '/finances/overview' && navigate(item.route)}
                className={`
                  p-4 rounded-xl shadow-sm transition-all
                  ${isCofrinho 
                    ? 'bg-gradient-to-br from-[#B4EB00] to-[#9FD700] cursor-pointer hover:shadow-md' 
                    : 'bg-white'
                  }
                  ${item.route !== '/finances/overview' && !isCofrinho ? 'cursor-pointer hover:shadow-md' : ''}
                `}
              >
                <div className="flex items-center gap-3 mb-2">
                  <div className={`p-2 rounded-lg ${isCofrinho ? 'bg-white/20' : 'bg-gray-50'}`}>
                    <Icon size={18} className={isCofrinho ? 'text-gray-900' : 'text-gray-600'} />
                  </div>
                  <span className={`text-xs ${isCofrinho ? 'text-gray-900 font-medium' : 'text-gray-600'}`}>
                    {item.title}
                  </span>
                </div>
                <p className={`text-xl font-bold ${isCofrinho ? 'text-gray-900' : 'text-gray-900'}`}>
                  {new Intl.NumberFormat('pt-BR', {
                    style: 'currency',
                    currency: 'BRL'
                  }).format(item.value)}
                </p>
                {item.trend && (
                  <p className={`text-xs mt-1 ${
                    isCofrinho 
                      ? 'text-gray-700'
                      : item.trend.value >= 0 ? 'text-[#4CAF50]' : 'text-[#FF3B30]'
                  }`}>
                    {item.trend.value > 0 ? '+' : ''}{item.trend.value}% {item.trend.label}
                  </p>
                )}
              </motion.div>
            );
          })}
        </div>

        {/* Annual Overview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <AnnualOverviewCard data={[]} />
        </motion.div>

        {/* Categories section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white rounded-2xl p-6 shadow-sm"
        >
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">
              Distribuição de gastos por categoria
            </h2>
            <button
              onClick={() => navigate('/finances/categories')}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <Edit2 size={20} className="text-gray-600" />
            </button>
          </div>

          {categoriesData && categoriesData.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Pie chart */}
              <div className="flex justify-center items-center">
                <PieChart
                  data={categoriesData.map(cat => ({
                    name: cat.name,
                    value: 100, // TODO: Calculate actual values from transactions
                    color: cat.color || '#B4EB00'
                  }))}
                  size={240}
                />
              </div>

              {/* Categories list */}
              <div className="space-y-3">
                {categoriesData.map((category) => (
                  <motion.div
                    key={category.id}
                    whileHover={{ scale: 1.02 }}
                    className="flex items-center justify-between p-3 rounded-xl hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: category.color || '#B4EB00' }}
                      />
                      <span className="font-medium">{category.name}</span>
                      <span className="text-xs px-2 py-1 bg-gray-100 rounded-full">
                        {category.transaction_type === 'income' ? 'Receita' : 'Despesa'}
                      </span>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">
                        {new Intl.NumberFormat('pt-BR', {
                          style: 'currency',
                          currency: 'BRL'
                        }).format(0)} {/* TODO: Calculate actual values */}
                      </p>
                      <p className="text-sm text-gray-500">
                        0% {/* TODO: Calculate percentage */}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <p>Nenhuma categoria encontrada</p>
              <button
                onClick={() => navigate('/finances/categories')}
                className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Criar categorias
              </button>
            </div>
          )}
        </motion.div>
      </div>

      {/* Add Transaction Modal */}
      <AddTransactionModal
        isOpen={isAddTransactionModalOpen}
        onClose={() => setIsAddTransactionModalOpen(false)}
        onAdd={handleAddTransaction}
      />

      {/* Edit Budget Modal */}
      <EditBudgetModal
        isOpen={isEditBudgetModalOpen}
        onClose={() => setIsEditBudgetModalOpen(false)}
        currentBudget={budget}
        onSave={handleBudgetUpdate}
      />
    </div>
  );
};

export default FinancesPage;