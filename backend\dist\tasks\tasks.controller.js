"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var TasksController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TasksController = void 0;
const common_1 = require("@nestjs/common");
const dev_auth_guard_1 = require("../auth/dev-auth.guard");
const tasks_service_1 = require("./tasks.service");
const create_task_dto_1 = require("./dto/create-task.dto");
const update_task_dto_1 = require("./dto/update-task.dto");
const create_task_category_dto_1 = require("./dto/create-task-category.dto");
const update_task_category_dto_1 = require("./dto/update-task-category.dto");
let TasksController = TasksController_1 = class TasksController {
    tasksService;
    logger = new common_1.Logger(TasksController_1.name);
    constructor(tasksService) {
        this.tasksService = tasksService;
    }
    async create(createTaskDto, req) {
        try {
            const userId = req.user?.userId;
            const userTimezone = await this.getUserTimezone(userId);
            if (!userId) {
                throw new common_1.HttpException('ID do usuário não encontrado', common_1.HttpStatus.UNAUTHORIZED);
            }
            return await this.tasksService.create(createTaskDto, userId, userTimezone);
        }
        catch (error) {
            this.logger.error('Erro ao criar tarefa:', error);
            throw new common_1.HttpException(error.message || 'Erro interno do servidor', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async findAll(req, page = 1, limit = 50) {
        try {
            const userId = req.user?.userId;
            const userTimezone = await this.getUserTimezone(userId);
            if (!userId) {
                throw new common_1.HttpException('ID do usuário não encontrado', common_1.HttpStatus.UNAUTHORIZED);
            }
            return await this.tasksService.findAll(userId, userTimezone, Number(page), Number(limit));
        }
        catch (error) {
            this.logger.error('Erro ao listar tarefas:', error);
            throw new common_1.HttpException(error.message || 'Erro interno do servidor', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async createCategory(createCategoryDto, req) {
        try {
            const userId = req.user?.userId;
            if (!userId) {
                throw new common_1.HttpException('ID do usuário não encontrado', common_1.HttpStatus.UNAUTHORIZED);
            }
            return await this.tasksService.createCategory(createCategoryDto, userId);
        }
        catch (error) {
            this.logger.error('Erro ao criar categoria:', error);
            throw new common_1.HttpException(error.message || 'Erro interno do servidor', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async findAllCategories(req) {
        try {
            const userId = req.user?.userId;
            if (!userId) {
                throw new common_1.HttpException('ID do usuário não encontrado', common_1.HttpStatus.UNAUTHORIZED);
            }
            return await this.tasksService.findAllCategories(userId);
        }
        catch (error) {
            this.logger.error('Erro ao listar categorias:', error);
            throw new common_1.HttpException(error.message || 'Erro interno do servidor', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async findOneCategory(id, req) {
        try {
            const userId = req.user?.userId;
            if (!userId) {
                throw new common_1.HttpException('ID do usuário não encontrado', common_1.HttpStatus.UNAUTHORIZED);
            }
            return await this.tasksService.findOneCategory(id, userId);
        }
        catch (error) {
            this.logger.error(`Erro ao buscar categoria ${id}:`, error);
            if (error.message.includes('não encontrada')) {
                throw new common_1.HttpException(error.message, common_1.HttpStatus.NOT_FOUND);
            }
            throw new common_1.HttpException(error.message || 'Erro interno do servidor', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updateCategory(id, updateCategoryDto, req) {
        try {
            const userId = req.user?.userId;
            if (!userId) {
                throw new common_1.HttpException('ID do usuário não encontrado', common_1.HttpStatus.UNAUTHORIZED);
            }
            return await this.tasksService.updateCategory(id, updateCategoryDto, userId);
        }
        catch (error) {
            this.logger.error(`Erro ao atualizar categoria ${id}:`, error);
            if (error.message.includes('não encontrada')) {
                throw new common_1.HttpException(error.message, common_1.HttpStatus.NOT_FOUND);
            }
            throw new common_1.HttpException(error.message || 'Erro interno do servidor', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async removeCategory(id, req) {
        try {
            const userId = req.user?.userId;
            if (!userId) {
                throw new common_1.HttpException('ID do usuário não encontrado', common_1.HttpStatus.UNAUTHORIZED);
            }
            await this.tasksService.removeCategory(id, userId);
            return { message: 'Categoria removida com sucesso' };
        }
        catch (error) {
            this.logger.error(`Erro ao remover categoria ${id}:`, error);
            if (error.message.includes('não encontrada')) {
                throw new common_1.HttpException(error.message, common_1.HttpStatus.NOT_FOUND);
            }
            if (error.message.includes('está sendo usada')) {
                throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
            }
            throw new common_1.HttpException(error.message || 'Erro interno do servidor', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async findOne(id, req) {
        try {
            const userId = req.user?.userId;
            const userTimezone = await this.getUserTimezone(userId);
            if (!userId) {
                throw new common_1.HttpException('ID do usuário não encontrado', common_1.HttpStatus.UNAUTHORIZED);
            }
            return await this.tasksService.findOne(id, userId, userTimezone);
        }
        catch (error) {
            this.logger.error(`Erro ao buscar tarefa ${id}:`, error);
            if (error.message.includes('não encontrada')) {
                throw new common_1.HttpException(error.message, common_1.HttpStatus.NOT_FOUND);
            }
            throw new common_1.HttpException(error.message || 'Erro interno do servidor', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async update(id, updateTaskDto, req) {
        try {
            const userId = req.user?.userId;
            const userTimezone = await this.getUserTimezone(userId);
            if (!userId) {
                throw new common_1.HttpException('ID do usuário não encontrado', common_1.HttpStatus.UNAUTHORIZED);
            }
            return await this.tasksService.update(id, updateTaskDto, userId, userTimezone);
        }
        catch (error) {
            this.logger.error(`Erro ao atualizar tarefa ${id}:`, error);
            if (error.message.includes('não encontrada')) {
                throw new common_1.HttpException(error.message, common_1.HttpStatus.NOT_FOUND);
            }
            throw new common_1.HttpException(error.message || 'Erro interno do servidor', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async remove(id, req) {
        try {
            const userId = req.user?.userId;
            if (!userId) {
                throw new common_1.HttpException('ID do usuário não encontrado', common_1.HttpStatus.UNAUTHORIZED);
            }
            await this.tasksService.remove(id, userId);
            return { message: 'Tarefa removida com sucesso' };
        }
        catch (error) {
            this.logger.error(`Erro ao remover tarefa ${id}:`, error);
            if (error.message.includes('não encontrada')) {
                throw new common_1.HttpException(error.message, common_1.HttpStatus.NOT_FOUND);
            }
            throw new common_1.HttpException(error.message || 'Erro interno do servidor', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async complete(id, req) {
        try {
            const userId = req.user?.userId;
            const userTimezone = await this.getUserTimezone(userId);
            if (!userId) {
                throw new common_1.HttpException('ID do usuário não encontrado', common_1.HttpStatus.UNAUTHORIZED);
            }
            return await this.tasksService.complete(id, userId, userTimezone);
        }
        catch (error) {
            this.logger.error(`Erro ao completar tarefa ${id}:`, error);
            if (error.message.includes('não encontrada')) {
                throw new common_1.HttpException(error.message, common_1.HttpStatus.NOT_FOUND);
            }
            throw new common_1.HttpException(error.message || 'Erro interno do servidor', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getUserTimezone(userId) {
        return 'America/Sao_Paulo';
    }
};
exports.TasksController = TasksController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_task_dto_1.CreateTaskDto, Object]),
    __metadata("design:returntype", Promise)
], TasksController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", Promise)
], TasksController.prototype, "findAll", null);
__decorate([
    (0, common_1.Post)('categories'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_task_category_dto_1.CreateTaskCategoryDto, Object]),
    __metadata("design:returntype", Promise)
], TasksController.prototype, "createCategory", null);
__decorate([
    (0, common_1.Get)('categories'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], TasksController.prototype, "findAllCategories", null);
__decorate([
    (0, common_1.Get)('categories/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], TasksController.prototype, "findOneCategory", null);
__decorate([
    (0, common_1.Put)('categories/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_task_category_dto_1.UpdateTaskCategoryDto, Object]),
    __metadata("design:returntype", Promise)
], TasksController.prototype, "updateCategory", null);
__decorate([
    (0, common_1.Delete)('categories/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], TasksController.prototype, "removeCategory", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], TasksController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_task_dto_1.UpdateTaskDto, Object]),
    __metadata("design:returntype", Promise)
], TasksController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], TasksController.prototype, "remove", null);
__decorate([
    (0, common_1.Patch)(':id/complete'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], TasksController.prototype, "complete", null);
exports.TasksController = TasksController = TasksController_1 = __decorate([
    (0, common_1.Controller)('tasks'),
    (0, common_1.UseGuards)(dev_auth_guard_1.DevAwareAuthGuard),
    __metadata("design:paramtypes", [tasks_service_1.TasksService])
], TasksController);
//# sourceMappingURL=tasks.controller.js.map