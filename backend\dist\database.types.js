"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.db = void 0;
const kysely_1 = require("kysely");
const mysql2_1 = require("mysql2");
const dotenv = require("dotenv");
dotenv.config();
exports.db = new kysely_1.Kysely({
    dialect: new kysely_1.MysqlDialect({
        pool: (0, mysql2_1.createPool)({
            host: process.env.DB_HOST,
            user: process.env.DB_USER,
            password: process.env.DB_PASSWORD,
            database: process.env.DB_DATABASE,
            timezone: '+00:00',
        }),
    }),
});
//# sourceMappingURL=database.types.js.map