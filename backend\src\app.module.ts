import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
// import { DatabaseModule } from './database.module';
import { AuthModule } from './auth/auth.module';
import { AppService } from './app.service';
import { DashboardModule } from './dashboard/dashboard.module';
import { TasksModule } from './tasks/tasks.module';
import { FinancesModule } from './finances/finances.module';
import { IdeasModule } from './ideas/ideas.module';
import { ConfigModule } from './config/config.module';

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    // DatabaseModule,
    AuthModule,
    DashboardModule,
    TasksModule,
    FinancesModule,
    IdeasModule,
    ConfigModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
