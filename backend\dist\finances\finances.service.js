"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var FinancesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.FinancesService = void 0;
const common_1 = require("@nestjs/common");
const database_types_1 = require("../database.types");
const timezone_1 = require("../utils/timezone");
let FinancesService = FinancesService_1 = class FinancesService {
    logger = new common_1.Logger(FinancesService_1.name);
    db = database_types_1.db;
    async create(createFinanceDto, userId, userTimezone) {
        try {
            const financeData = {
                ...createFinanceDto,
                user_id: userId,
                transaction_date: (0, timezone_1.fromUserTimezone)(new Date(createFinanceDto.transaction_date), userTimezone),
                created_at: new Date(),
                updated_at: new Date()
            };
            const result = await this.db
                .insertInto('finances')
                .values(financeData)
                .executeTakeFirst();
            if (!result.insertId) {
                throw new Error('Falha ao criar transação financeira');
            }
            this.logger.log(`Transação financeira criada com ID ${result.insertId} para usuário ${userId}`);
            return this.findOne(Number(result.insertId), userId, userTimezone);
        }
        catch (error) {
            this.logger.error(`Erro ao criar transação financeira para usuário ${userId}:`, error);
            throw new Error(`Erro ao criar transação financeira: ${error.message}`);
        }
    }
    async findAll(userId, userTimezone, page = 1, limit = 50) {
        try {
            const offset = (page - 1) * limit;
            const finances = await this.db
                .selectFrom('finances')
                .leftJoin('finances_categories', 'finances.category_id', 'finances_categories.id')
                .select([
                'finances.id',
                'finances.transaction_type',
                'finances.category_id',
                'finances_categories.name as category_name',
                'finances.is_saving',
                'finances.description',
                'finances.amount',
                'finances.transaction_date',
                'finances.user_id',
                'finances.created_at',
                'finances.updated_at'
            ])
                .where('finances.user_id', '=', userId)
                .orderBy('finances.transaction_date', 'desc')
                .limit(limit)
                .offset(offset)
                .execute();
            const totalResult = await this.db
                .selectFrom('finances')
                .select(this.db.fn.count('id').as('count'))
                .where('user_id', '=', userId)
                .executeTakeFirst();
            const total = Number(totalResult?.count || 0);
            const financesWithTimezone = finances.map(finance => ({
                id: finance.id,
                transaction_type: finance.transaction_type,
                category_id: finance.category_id || undefined,
                category_name: finance.category_name || undefined,
                is_saving: finance.is_saving || undefined,
                description: finance.description || undefined,
                amount: finance.amount,
                transaction_date: (0, timezone_1.toUserTimezone)(finance.transaction_date, userTimezone),
                user_id: finance.user_id,
                created_at: (0, timezone_1.toUserTimezone)(finance.created_at, userTimezone),
                updated_at: (0, timezone_1.toUserTimezone)(finance.updated_at, userTimezone)
            }));
            this.logger.debug(`Listadas ${finances.length} transações para usuário ${userId}`);
            return {
                finances: financesWithTimezone,
                total,
                page,
                limit
            };
        }
        catch (error) {
            this.logger.error(`Erro ao listar transações para usuário ${userId}:`, error);
            throw new Error(`Erro ao listar transações: ${error.message}`);
        }
    }
    async findOne(id, userId, userTimezone) {
        try {
            const finance = await this.db
                .selectFrom('finances')
                .leftJoin('finances_categories', 'finances.category_id', 'finances_categories.id')
                .select([
                'finances.id',
                'finances.transaction_type',
                'finances.category_id',
                'finances_categories.name as category_name',
                'finances.is_saving',
                'finances.description',
                'finances.amount',
                'finances.transaction_date',
                'finances.user_id',
                'finances.created_at',
                'finances.updated_at'
            ])
                .where('finances.id', '=', id)
                .where('finances.user_id', '=', userId)
                .executeTakeFirst();
            if (!finance) {
                throw new common_1.NotFoundException(`Transação financeira com ID ${id} não encontrada`);
            }
            return {
                id: finance.id,
                transaction_type: finance.transaction_type,
                category_id: finance.category_id || undefined,
                category_name: finance.category_name || undefined,
                is_saving: finance.is_saving || undefined,
                description: finance.description || undefined,
                amount: finance.amount,
                transaction_date: (0, timezone_1.toUserTimezone)(finance.transaction_date, userTimezone),
                user_id: finance.user_id,
                created_at: (0, timezone_1.toUserTimezone)(finance.created_at, userTimezone),
                updated_at: (0, timezone_1.toUserTimezone)(finance.updated_at, userTimezone)
            };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            this.logger.error(`Erro ao buscar transação ${id} para usuário ${userId}:`, error);
            throw new Error(`Erro ao buscar transação: ${error.message}`);
        }
    }
    async update(id, updateFinanceDto, userId, userTimezone) {
        try {
            await this.findOne(id, userId, userTimezone);
            const updateData = {
                ...updateFinanceDto,
                transaction_date: updateFinanceDto.transaction_date ? (0, timezone_1.fromUserTimezone)(new Date(updateFinanceDto.transaction_date), userTimezone) : undefined,
                updated_at: new Date()
            };
            Object.keys(updateData).forEach(key => {
                if (updateData[key] === undefined) {
                    delete updateData[key];
                }
            });
            await this.db
                .updateTable('finances')
                .set(updateData)
                .where('id', '=', id)
                .where('user_id', '=', userId)
                .execute();
            this.logger.log(`Transação ${id} atualizada para usuário ${userId}`);
            return this.findOne(id, userId, userTimezone);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            this.logger.error(`Erro ao atualizar transação ${id} para usuário ${userId}:`, error);
            throw new Error(`Erro ao atualizar transação: ${error.message}`);
        }
    }
    async remove(id, userId) {
        try {
            await this.findOne(id, userId, 'UTC');
            await this.db
                .deleteFrom('finances')
                .where('id', '=', id)
                .where('user_id', '=', userId)
                .execute();
            this.logger.log(`Transação ${id} removida para usuário ${userId}`);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            this.logger.error(`Erro ao remover transação ${id} para usuário ${userId}:`, error);
            throw new Error(`Erro ao remover transação: ${error.message}`);
        }
    }
    async getSummary(userId, userTimezone, startDate, endDate) {
        try {
            const now = new Date();
            const start = startDate || new Date(now.getFullYear(), now.getMonth(), 1);
            const end = endDate || new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
            const startUTC = (0, timezone_1.fromUserTimezone)(start, userTimezone);
            const endUTC = (0, timezone_1.fromUserTimezone)(end, userTimezone);
            const transactions = await this.db
                .selectFrom('finances')
                .select(['transaction_type', 'amount', 'is_saving'])
                .where('user_id', '=', userId)
                .where('transaction_date', '>=', startUTC)
                .where('transaction_date', '<=', endUTC)
                .execute();
            let totalIncome = 0;
            let totalExpenses = 0;
            let totalSavings = 0;
            transactions.forEach(transaction => {
                const amount = parseFloat(transaction.amount);
                if (transaction.transaction_type === 'income') {
                    totalIncome += amount;
                }
                else if (transaction.transaction_type === 'expense') {
                    totalExpenses += amount;
                    if (transaction.is_saving) {
                        totalSavings += amount;
                    }
                }
            });
            const balance = totalIncome - totalExpenses;
            this.logger.debug(`Resumo financeiro para usuário ${userId}: receita=${totalIncome}, gastos=${totalExpenses}, economia=${totalSavings}`);
            return {
                totalIncome,
                totalExpenses,
                totalSavings,
                balance,
                period: {
                    start: (0, timezone_1.toUserTimezone)(startUTC, userTimezone),
                    end: (0, timezone_1.toUserTimezone)(endUTC, userTimezone)
                }
            };
        }
        catch (error) {
            this.logger.error(`Erro ao buscar resumo financeiro para usuário ${userId}:`, error);
            throw new Error(`Erro ao buscar resumo financeiro: ${error.message}`);
        }
    }
};
exports.FinancesService = FinancesService;
exports.FinancesService = FinancesService = FinancesService_1 = __decorate([
    (0, common_1.Injectable)()
], FinancesService);
//# sourceMappingURL=finances.service.js.map