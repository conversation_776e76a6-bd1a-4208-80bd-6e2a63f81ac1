import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
import { TaskResponseDto, TaskListResponseDto } from './dto/task-response.dto';
import { CreateTaskCategoryDto } from './dto/create-task-category.dto';
import { UpdateTaskCategoryDto } from './dto/update-task-category.dto';
import { TaskCategoryResponseDto } from './dto/task-category-response.dto';
export declare class TasksService {
    private readonly logger;
    private db;
    create(createTaskDto: CreateTaskDto, userId: number, userTimezone: string): Promise<TaskResponseDto>;
    findAll(userId: number, userTimezone: string, page?: number, limit?: number): Promise<TaskListResponseDto>;
    findOne(id: number, userId: number, userTimezone: string): Promise<TaskResponseDto>;
    update(id: number, updateTaskDto: UpdateTaskDto, userId: number, userTimezone: string): Promise<TaskResponseDto>;
    remove(id: number, userId: number): Promise<void>;
    complete(id: number, userId: number, userTimezone: string): Promise<TaskResponseDto>;
    createCategory(createCategoryDto: CreateTaskCategoryDto, userId: number): Promise<TaskCategoryResponseDto>;
    findAllCategories(userId: number): Promise<TaskCategoryResponseDto[]>;
    findOneCategory(id: number, userId: number): Promise<TaskCategoryResponseDto>;
    updateCategory(id: number, updateCategoryDto: UpdateTaskCategoryDto, userId: number): Promise<TaskCategoryResponseDto>;
    removeCategory(id: number, userId: number): Promise<void>;
}
