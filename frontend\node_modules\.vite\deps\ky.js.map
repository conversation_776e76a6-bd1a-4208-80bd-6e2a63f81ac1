{"version": 3, "sources": ["../../ky/source/errors/HTTPError.ts", "../../ky/source/errors/TimeoutError.ts", "../../ky/source/core/constants.ts", "../../ky/source/utils/body.ts", "../../ky/source/utils/is.ts", "../../ky/source/utils/merge.ts", "../../ky/source/utils/normalize.ts", "../../ky/source/utils/timeout.ts", "../../ky/source/utils/delay.ts", "../../ky/source/utils/options.ts", "../../ky/source/core/Ky.ts", "../../ky/source/index.ts"], "sourcesContent": ["import type {NormalizedOptions} from '../types/options.js';\nimport type {KyRequest} from '../types/request.js';\nimport type {KyResponse} from '../types/response.js';\n\nexport class HTTPError<T = unknown> extends Error {\n\tpublic response: KyResponse<T>;\n\tpublic request: KyRequest;\n\tpublic options: NormalizedOptions;\n\n\tconstructor(response: Response, request: Request, options: NormalizedOptions) {\n\t\tconst code = (response.status || response.status === 0) ? response.status : '';\n\t\tconst title = response.statusText || '';\n\t\tconst status = `${code} ${title}`.trim();\n\t\tconst reason = status ? `status code ${status}` : 'an unknown error';\n\n\t\tsuper(`Request failed with ${reason}: ${request.method} ${request.url}`);\n\n\t\tthis.name = 'HTTPError';\n\t\tthis.response = response;\n\t\tthis.request = request;\n\t\tthis.options = options;\n\t}\n}\n", "import type {KyRequest} from '../types/request.js';\n\nexport class TimeoutError extends Error {\n\tpublic request: KyRequest;\n\n\tconstructor(request: Request) {\n\t\tsuper(`Request timed out: ${request.method} ${request.url}`);\n\t\tthis.name = 'TimeoutError';\n\t\tthis.request = request;\n\t}\n}\n", "import type {Expect, Equal} from '@type-challenges/utils';\nimport {type HttpMethod, type KyOptionsRegistry} from '../types/options.js';\nimport {type RequestInitRegistry} from '../types/request.js';\n\nexport const supportsRequestStreams = (() => {\n\tlet duplexAccessed = false;\n\tlet hasContentType = false;\n\tconst supportsReadableStream = typeof globalThis.ReadableStream === 'function';\n\tconst supportsRequest = typeof globalThis.Request === 'function';\n\n\tif (supportsReadableStream && supportsRequest) {\n\t\ttry {\n\t\t\thasContentType = new globalThis.Request('https://empty.invalid', {\n\t\t\t\tbody: new globalThis.ReadableStream(),\n\t\t\t\tmethod: 'POST',\n\t\t\t\t// @ts-expect-error - Types are outdated.\n\t\t\t\tget duplex() {\n\t\t\t\t\tduplexAccessed = true;\n\t\t\t\t\treturn 'half';\n\t\t\t\t},\n\t\t\t}).headers.has('Content-Type');\n\t\t} catch (error) {\n\t\t\t// QQBrowser on iOS throws \"unsupported BodyInit type\" error (see issue #581)\n\t\t\tif (error instanceof Error && error.message === 'unsupported BodyInit type') {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\tthrow error;\n\t\t}\n\t}\n\n\treturn duplexAccessed && !hasContentType;\n})();\n\nexport const supportsAbortController = typeof globalThis.AbortController === 'function';\nexport const supportsResponseStreams = typeof globalThis.ReadableStream === 'function';\nexport const supportsFormData = typeof globalThis.FormData === 'function';\n\nexport const requestMethods = ['get', 'post', 'put', 'patch', 'head', 'delete'] as const;\n\nconst validate = <T extends Array<true>>() => undefined as unknown as T;\nvalidate<[\n\tExpect<Equal<typeof requestMethods[number], HttpMethod>>,\n]>();\n\nexport const responseTypes = {\n\tjson: 'application/json',\n\ttext: 'text/*',\n\tformData: 'multipart/form-data',\n\tarrayBuffer: '*/*',\n\tblob: '*/*',\n} as const;\n\n// The maximum value of a 32bit int (see issue #117)\nexport const maxSafeTimeout = 2_147_483_647;\n\n// Size in bytes of a typical form boundary, used to help estimate upload size\nexport const usualFormBoundarySize = new TextEncoder().encode('------WebKitFormBoundaryaxpyiPgbbPti10Rw').length;\n\nexport const stop = Symbol('stop');\n\nexport const kyOptionKeys: KyOptionsRegistry = {\n\tjson: true,\n\tparseJson: true,\n\tstringifyJson: true,\n\tsearchParams: true,\n\tprefixUrl: true,\n\tretry: true,\n\ttimeout: true,\n\thooks: true,\n\tthrowHttpErrors: true,\n\tonDownloadProgress: true,\n\tonUploadProgress: true,\n\tfetch: true,\n};\n\nexport const requestOptionsRegistry: RequestInitRegistry = {\n\tmethod: true,\n\theaders: true,\n\tbody: true,\n\tmode: true,\n\tcredentials: true,\n\tcache: true,\n\tredirect: true,\n\treferrer: true,\n\treferrerPolicy: true,\n\tintegrity: true,\n\tkeepalive: true,\n\tsignal: true,\n\twindow: true,\n\tdispatcher: true,\n\tduplex: true,\n\tpriority: true,\n};\n", "import type {Options} from '../types/options.js';\nimport {usualFormBoundarySize} from '../core/constants.js';\n\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport const getBodySize = (body?: BodyInit | null): number => {\n\tif (!body) {\n\t\treturn 0;\n\t}\n\n\tif (body instanceof FormData) {\n\t\t// This is an approximation, as FormData size calculation is not straightforward\n\t\tlet size = 0;\n\n\t\tfor (const [key, value] of body) {\n\t\t\tsize += usualFormBoundarySize;\n\t\t\tsize += new TextEncoder().encode(`Content-Disposition: form-data; name=\"${key}\"`).length;\n\t\t\tsize += typeof value === 'string'\n\t\t\t\t? new TextEncoder().encode(value).length\n\t\t\t\t: value.size;\n\t\t}\n\n\t\treturn size;\n\t}\n\n\tif (body instanceof Blob) {\n\t\treturn body.size;\n\t}\n\n\tif (body instanceof ArrayBuffer) {\n\t\treturn body.byteLength;\n\t}\n\n\tif (typeof body === 'string') {\n\t\treturn new TextEncoder().encode(body).length;\n\t}\n\n\tif (body instanceof URLSearchParams) {\n\t\treturn new TextEncoder().encode(body.toString()).length;\n\t}\n\n\tif ('byteLength' in body) {\n\t\treturn (body).byteLength;\n\t}\n\n\tif (typeof body === 'object' && body !== null) {\n\t\ttry {\n\t\t\tconst jsonString = JSON.stringify(body);\n\t\t\treturn new TextEncoder().encode(jsonString).length;\n\t\t} catch {\n\t\t\treturn 0;\n\t\t}\n\t}\n\n\treturn 0; // Default case, unable to determine size\n};\n\nexport const streamResponse = (response: Response, onDownloadProgress: Options['onDownloadProgress']) => {\n\tconst totalBytes = Number(response.headers.get('content-length')) || 0;\n\tlet transferredBytes = 0;\n\n\tif (response.status === 204) {\n\t\tif (onDownloadProgress) {\n\t\t\tonDownloadProgress({percent: 1, totalBytes, transferredBytes}, new Uint8Array());\n\t\t}\n\n\t\treturn new Response(\n\t\t\tnull,\n\t\t\t{\n\t\t\t\tstatus: response.status,\n\t\t\t\tstatusText: response.statusText,\n\t\t\t\theaders: response.headers,\n\t\t\t},\n\t\t);\n\t}\n\n\treturn new Response(\n\t\tnew ReadableStream({\n\t\t\tasync start(controller) {\n\t\t\t\tconst reader = response.body!.getReader();\n\n\t\t\t\tif (onDownloadProgress) {\n\t\t\t\t\tonDownloadProgress({percent: 0, transferredBytes: 0, totalBytes}, new Uint8Array());\n\t\t\t\t}\n\n\t\t\t\tasync function read() {\n\t\t\t\t\tconst {done, value} = await reader.read();\n\t\t\t\t\tif (done) {\n\t\t\t\t\t\tcontroller.close();\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (onDownloadProgress) {\n\t\t\t\t\t\ttransferredBytes += value.byteLength;\n\t\t\t\t\t\tconst percent = totalBytes === 0 ? 0 : transferredBytes / totalBytes;\n\t\t\t\t\t\tonDownloadProgress({percent, transferredBytes, totalBytes}, value);\n\t\t\t\t\t}\n\n\t\t\t\t\tcontroller.enqueue(value);\n\t\t\t\t\tawait read();\n\t\t\t\t}\n\n\t\t\t\tawait read();\n\t\t\t},\n\t\t}),\n\t\t{\n\t\t\tstatus: response.status,\n\t\t\tstatusText: response.statusText,\n\t\t\theaders: response.headers,\n\t\t},\n\t);\n};\n\nexport const streamRequest = (request: Request, onUploadProgress: Options['onUploadProgress']) => {\n\tconst totalBytes = getBodySize(request.body);\n\tlet transferredBytes = 0;\n\n\treturn new Request(request, {\n\t\t// @ts-expect-error - Types are outdated.\n\t\tduplex: 'half',\n\t\tbody: new ReadableStream({\n\t\t\tasync start(controller) {\n\t\t\t\tconst reader = request.body instanceof ReadableStream ? request.body.getReader() : new Response('').body!.getReader();\n\n\t\t\t\tasync function read() {\n\t\t\t\t\tconst {done, value} = await reader.read();\n\t\t\t\t\tif (done) {\n\t\t\t\t\t\t// Ensure 100% progress is reported when the upload is complete\n\t\t\t\t\t\tif (onUploadProgress) {\n\t\t\t\t\t\t\tonUploadProgress({percent: 1, transferredBytes, totalBytes: Math.max(totalBytes, transferredBytes)}, new Uint8Array());\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tcontroller.close();\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\ttransferredBytes += value.byteLength;\n\t\t\t\t\tlet percent = totalBytes === 0 ? 0 : transferredBytes / totalBytes;\n\t\t\t\t\tif (totalBytes < transferredBytes || percent === 1) {\n\t\t\t\t\t\tpercent = 0.99;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (onUploadProgress) {\n\t\t\t\t\t\tonUploadProgress({percent: Number(percent.toFixed(2)), transferredBytes, totalBytes}, value);\n\t\t\t\t\t}\n\n\t\t\t\t\tcontroller.enqueue(value);\n\t\t\t\t\tawait read();\n\t\t\t\t}\n\n\t\t\t\tawait read();\n\t\t\t},\n\t\t}),\n\t});\n};\n", "// eslint-disable-next-line @typescript-eslint/ban-types\nexport const isObject = (value: unknown): value is object => value !== null && typeof value === 'object';\n", "import type {KyHeadersInit, Options} from '../types/options.js';\nimport type {Hooks} from '../types/hooks.js';\nimport {isObject} from './is.js';\n\nexport const validateAndMerge = (...sources: Array<Partial<Options> | undefined>): Partial<Options> => {\n\tfor (const source of sources) {\n\t\tif ((!isObject(source) || Array.isArray(source)) && source !== undefined) {\n\t\t\tthrow new TypeError('The `options` argument must be an object');\n\t\t}\n\t}\n\n\treturn deepMerge({}, ...sources);\n};\n\nexport const mergeHeaders = (source1: KyHeadersInit = {}, source2: KyHeadersInit = {}) => {\n\tconst result = new globalThis.Headers(source1 as RequestInit['headers']);\n\tconst isHeadersInstance = source2 instanceof globalThis.Headers;\n\tconst source = new globalThis.Headers(source2 as RequestInit['headers']);\n\n\tfor (const [key, value] of source.entries()) {\n\t\tif ((isHeadersInstance && value === 'undefined') || value === undefined) {\n\t\t\tresult.delete(key);\n\t\t} else {\n\t\t\tresult.set(key, value);\n\t\t}\n\t}\n\n\treturn result;\n};\n\nfunction newHookValue<K extends keyof Hooks>(original: Hooks, incoming: Hooks, property: K): Required<Hooks>[K] {\n\treturn (Object.hasOwn(incoming, property) && incoming[property] === undefined)\n\t\t? []\n\t\t: deepMerge<Required<Hooks>[K]>(original[property] ?? [], incoming[property] ?? []);\n}\n\nexport const mergeHooks = (original: Hooks = {}, incoming: Hooks = {}): Required<Hooks> => (\n\t{\n\t\tbeforeRequest: newHookValue(original, incoming, 'beforeRequest'),\n\t\tbeforeRetry: newHookValue(original, incoming, 'beforeRetry'),\n\t\tafterResponse: newHookValue(original, incoming, 'afterResponse'),\n\t\tbeforeError: newHookValue(original, incoming, 'beforeError'),\n\t}\n);\n\n// TODO: Make this strongly-typed (no `any`).\nexport const deepMerge = <T>(...sources: Array<Partial<T> | undefined>): T => {\n\tlet returnValue: any = {};\n\tlet headers = {};\n\tlet hooks = {};\n\n\tfor (const source of sources) {\n\t\tif (Array.isArray(source)) {\n\t\t\tif (!Array.isArray(returnValue)) {\n\t\t\t\treturnValue = [];\n\t\t\t}\n\n\t\t\treturnValue = [...returnValue, ...source];\n\t\t} else if (isObject(source)) {\n\t\t\tfor (let [key, value] of Object.entries(source)) {\n\t\t\t\tif (isObject(value) && key in returnValue) {\n\t\t\t\t\tvalue = deepMerge(returnValue[key], value);\n\t\t\t\t}\n\n\t\t\t\treturnValue = {...returnValue, [key]: value};\n\t\t\t}\n\n\t\t\tif (isObject((source as any).hooks)) {\n\t\t\t\thooks = mergeHooks(hooks, (source as any).hooks);\n\t\t\t\treturnValue.hooks = hooks;\n\t\t\t}\n\n\t\t\tif (isObject((source as any).headers)) {\n\t\t\t\theaders = mergeHeaders(headers, (source as any).headers);\n\t\t\t\treturnValue.headers = headers;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn returnValue;\n};\n", "import {requestMethods} from '../core/constants.js';\nimport type {HttpMethod} from '../types/options.js';\nimport type {RetryOptions} from '../types/retry.js';\n\nexport const normalizeRequestMethod = (input: string): string =>\n\trequestMethods.includes(input as HttpMethod) ? input.toUpperCase() : input;\n\nconst retryMethods = ['get', 'put', 'head', 'delete', 'options', 'trace'];\n\nconst retryStatusCodes = [408, 413, 429, 500, 502, 503, 504];\n\nconst retryAfterStatusCodes = [413, 429, 503];\n\nconst defaultRetryOptions: Required<RetryOptions> = {\n\tlimit: 2,\n\tmethods: retryMethods,\n\tstatusCodes: retryStatusCodes,\n\tafterStatusCodes: retryAfterStatusCodes,\n\tmaxRetryAfter: Number.POSITIVE_INFINITY,\n\tbackoffLimit: Number.POSITIVE_INFINITY,\n\tdelay: attemptCount => 0.3 * (2 ** (attemptCount - 1)) * 1000,\n};\n\nexport const normalizeRetryOptions = (retry: number | RetryOptions = {}): Required<RetryOptions> => {\n\tif (typeof retry === 'number') {\n\t\treturn {\n\t\t\t...defaultRetryOptions,\n\t\t\tlimit: retry,\n\t\t};\n\t}\n\n\tif (retry.methods && !Array.isArray(retry.methods)) {\n\t\tthrow new Error('retry.methods must be an array');\n\t}\n\n\tif (retry.statusCodes && !Array.isArray(retry.statusCodes)) {\n\t\tthrow new Error('retry.statusCodes must be an array');\n\t}\n\n\treturn {\n\t\t...defaultRetryOptions,\n\t\t...retry,\n\t};\n};\n", "import {TimeoutError} from '../errors/TimeoutError.js';\n\nexport type TimeoutOptions = {\n\ttimeout: number;\n\tfetch: typeof fetch;\n};\n\n// `Promise.race()` workaround (#91)\nexport default async function timeout(\n\trequest: Request,\n\tinit: RequestInit,\n\tabortController: AbortController | undefined,\n\toptions: TimeoutOptions,\n): Promise<Response> {\n\treturn new Promise((resolve, reject) => {\n\t\tconst timeoutId = setTimeout(() => {\n\t\t\tif (abortController) {\n\t\t\t\tabortController.abort();\n\t\t\t}\n\n\t\t\treject(new TimeoutError(request));\n\t\t}, options.timeout);\n\n\t\tvoid options\n\t\t\t.fetch(request, init)\n\t\t\t.then(resolve)\n\t\t\t.catch(reject)\n\t\t\t.then(() => {\n\t\t\t\tclearTimeout(timeoutId);\n\t\t\t});\n\t});\n}\n", "// https://github.com/sindresorhus/delay/tree/ab98ae8dfcb38e1593286c94d934e70d14a4e111\n\nimport {type InternalOptions} from '../types/options.js';\n\nexport type DelayOptions = {\n\tsignal?: InternalOptions['signal'];\n};\n\nexport default async function delay(\n\tms: number,\n\t{signal}: DelayOptions,\n): Promise<void> {\n\treturn new Promise((resolve, reject) => {\n\t\tif (signal) {\n\t\t\tsignal.throwIfAborted();\n\t\t\tsignal.addEventListener('abort', abortHandler, {once: true});\n\t\t}\n\n\t\tfunction abortHandler() {\n\t\t\tclearTimeout(timeoutId);\n\t\t\treject(signal!.reason as Error);\n\t\t}\n\n\t\tconst timeoutId = setTimeout(() => {\n\t\t\tsignal?.removeEventListener('abort', abortHandler);\n\t\t\tresolve();\n\t\t}, ms);\n\t});\n}\n", "import {kyOptionKeys, requestOptionsRegistry} from '../core/constants.js';\n\nexport const findUnknownOptions = (\n\trequest: Request,\n\toptions: Record<string, unknown>,\n): Record<string, unknown> => {\n\tconst unknownOptions: Record<string, unknown> = {};\n\n\tfor (const key in options) {\n\t\tif (!(key in requestOptionsRegistry) && !(key in kyOptionKeys) && !(key in request)) {\n\t\t\tunknownOptions[key] = options[key];\n\t\t}\n\t}\n\n\treturn unknownOptions;\n};\n", "import {HTTPError} from '../errors/HTTPError.js';\nimport {TimeoutError} from '../errors/TimeoutError.js';\nimport type {\n\tInput,\n\tInternalOptions,\n\tNormalizedOptions,\n\tOptions,\n\tSearchParamsInit,\n} from '../types/options.js';\nimport {type ResponsePromise} from '../types/ResponsePromise.js';\nimport {streamRequest, streamResponse} from '../utils/body.js';\nimport {mergeHeaders, mergeHooks} from '../utils/merge.js';\nimport {normalizeRequestMethod, normalizeRetryOptions} from '../utils/normalize.js';\nimport timeout, {type TimeoutOptions} from '../utils/timeout.js';\nimport delay from '../utils/delay.js';\nimport {type ObjectEntries} from '../utils/types.js';\nimport {findUnknownOptions} from '../utils/options.js';\nimport {\n\tmaxSafeTimeout,\n\tresponseTypes,\n\tstop,\n\tsupports<PERSON>bort<PERSON>ontroller,\n\tsupportsFormData,\n\tsupportsResponseStreams,\n\tsupportsRequestStreams,\n} from './constants.js';\n\nexport class Ky {\n\tstatic create(input: Input, options: Options): ResponsePromise {\n\t\tconst ky = new Ky(input, options);\n\n\t\tconst function_ = async (): Promise<Response> => {\n\t\t\tif (typeof ky._options.timeout === 'number' && ky._options.timeout > maxSafeTimeout) {\n\t\t\t\tthrow new RangeError(`The \\`timeout\\` option cannot be greater than ${maxSafeTimeout}`);\n\t\t\t}\n\n\t\t\t// Delay the fetch so that body method shortcuts can set the Accept header\n\t\t\tawait Promise.resolve();\n\t\t\t// Before using ky.request, _fetch clones it and saves the clone for future retries to use.\n\t\t\t// If retry is not needed, close the cloned request's ReadableStream for memory safety.\n\t\t\tlet response = await ky._fetch();\n\n\t\t\tfor (const hook of ky._options.hooks.afterResponse) {\n\t\t\t\t// eslint-disable-next-line no-await-in-loop\n\t\t\t\tconst modifiedResponse = await hook(\n\t\t\t\t\tky.request,\n\t\t\t\t\tky._options as NormalizedOptions,\n\t\t\t\t\tky._decorateResponse(response.clone()),\n\t\t\t\t);\n\n\t\t\t\tif (modifiedResponse instanceof globalThis.Response) {\n\t\t\t\t\tresponse = modifiedResponse;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tky._decorateResponse(response);\n\n\t\t\tif (!response.ok && ky._options.throwHttpErrors) {\n\t\t\t\tlet error = new HTTPError(response, ky.request, ky._options as NormalizedOptions);\n\n\t\t\t\tfor (const hook of ky._options.hooks.beforeError) {\n\t\t\t\t\t// eslint-disable-next-line no-await-in-loop\n\t\t\t\t\terror = await hook(error);\n\t\t\t\t}\n\n\t\t\t\tthrow error;\n\t\t\t}\n\n\t\t\t// If `onDownloadProgress` is passed, it uses the stream API internally\n\t\t\tif (ky._options.onDownloadProgress) {\n\t\t\t\tif (typeof ky._options.onDownloadProgress !== 'function') {\n\t\t\t\t\tthrow new TypeError('The `onDownloadProgress` option must be a function');\n\t\t\t\t}\n\n\t\t\t\tif (!supportsResponseStreams) {\n\t\t\t\t\tthrow new Error('Streams are not supported in your environment. `ReadableStream` is missing.');\n\t\t\t\t}\n\n\t\t\t\treturn streamResponse(response.clone(), ky._options.onDownloadProgress);\n\t\t\t}\n\n\t\t\treturn response;\n\t\t};\n\n\t\tconst isRetriableMethod = ky._options.retry.methods.includes(ky.request.method.toLowerCase());\n\t\tconst result = (isRetriableMethod ? ky._retry(function_) : function_())\n\t\t\t.finally(async () => {\n\t\t\t\t// Now that we know a retry is not needed, close the ReadableStream of the cloned request.\n\t\t\t\tif (!ky.request.bodyUsed) {\n\t\t\t\t\tawait ky.request.body?.cancel();\n\t\t\t\t}\n\t\t\t}) as ResponsePromise;\n\n\t\tfor (const [type, mimeType] of Object.entries(responseTypes) as ObjectEntries<typeof responseTypes>) {\n\t\t\tresult[type] = async () => {\n\t\t\t\t// eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing\n\t\t\t\tky.request.headers.set('accept', ky.request.headers.get('accept') || mimeType);\n\n\t\t\t\tconst response = await result;\n\n\t\t\t\tif (type === 'json') {\n\t\t\t\t\tif (response.status === 204) {\n\t\t\t\t\t\treturn '';\n\t\t\t\t\t}\n\n\t\t\t\t\tconst arrayBuffer = await response.clone().arrayBuffer();\n\t\t\t\t\tconst responseSize = arrayBuffer.byteLength;\n\t\t\t\t\tif (responseSize === 0) {\n\t\t\t\t\t\treturn '';\n\t\t\t\t\t}\n\n\t\t\t\t\tif (options.parseJson) {\n\t\t\t\t\t\treturn options.parseJson(await response.text());\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturn response[type]();\n\t\t\t};\n\t\t}\n\n\t\treturn result;\n\t}\n\n\tpublic request: Request;\n\tprotected abortController?: AbortController;\n\tprotected _retryCount = 0;\n\tprotected _input: Input;\n\tprotected _options: InternalOptions;\n\n\t// eslint-disable-next-line complexity\n\tconstructor(input: Input, options: Options = {}) {\n\t\tthis._input = input;\n\n\t\tthis._options = {\n\t\t\t...options,\n\t\t\theaders: mergeHeaders((this._input as Request).headers, options.headers),\n\t\t\thooks: mergeHooks(\n\t\t\t\t{\n\t\t\t\t\tbeforeRequest: [],\n\t\t\t\t\tbeforeRetry: [],\n\t\t\t\t\tbeforeError: [],\n\t\t\t\t\tafterResponse: [],\n\t\t\t\t},\n\t\t\t\toptions.hooks,\n\t\t\t),\n\t\t\tmethod: normalizeRequestMethod(options.method ?? (this._input as Request).method ?? 'GET'),\n\t\t\t// eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing\n\t\t\tprefixUrl: String(options.prefixUrl || ''),\n\t\t\tretry: normalizeRetryOptions(options.retry),\n\t\t\tthrowHttpErrors: options.throwHttpErrors !== false,\n\t\t\ttimeout: options.timeout ?? 10_000,\n\t\t\tfetch: options.fetch ?? globalThis.fetch.bind(globalThis),\n\t\t};\n\n\t\tif (typeof this._input !== 'string' && !(this._input instanceof URL || this._input instanceof globalThis.Request)) {\n\t\t\tthrow new TypeError('`input` must be a string, URL, or Request');\n\t\t}\n\n\t\tif (this._options.prefixUrl && typeof this._input === 'string') {\n\t\t\tif (this._input.startsWith('/')) {\n\t\t\t\tthrow new Error('`input` must not begin with a slash when using `prefixUrl`');\n\t\t\t}\n\n\t\t\tif (!this._options.prefixUrl.endsWith('/')) {\n\t\t\t\tthis._options.prefixUrl += '/';\n\t\t\t}\n\n\t\t\tthis._input = this._options.prefixUrl + this._input;\n\t\t}\n\n\t\tif (supportsAbortController) {\n\t\t\tconst originalSignal = this._options.signal ?? (this._input as Request).signal;\n\t\t\tthis.abortController = new globalThis.AbortController();\n\t\t\tthis._options.signal = originalSignal ? AbortSignal.any([originalSignal, this.abortController.signal]) : this.abortController.signal;\n\t\t}\n\n\t\tif (supportsRequestStreams) {\n\t\t\t// @ts-expect-error - Types are outdated.\n\t\t\tthis._options.duplex = 'half';\n\t\t}\n\n\t\tif (this._options.json !== undefined) {\n\t\t\tthis._options.body = this._options.stringifyJson?.(this._options.json) ?? JSON.stringify(this._options.json);\n\t\t\tthis._options.headers.set('content-type', this._options.headers.get('content-type') ?? 'application/json');\n\t\t}\n\n\t\tthis.request = new globalThis.Request(this._input, this._options);\n\n\t\tif (this._options.searchParams) {\n\t\t\t// eslint-disable-next-line unicorn/prevent-abbreviations\n\t\t\tconst textSearchParams = typeof this._options.searchParams === 'string'\n\t\t\t\t? this._options.searchParams.replace(/^\\?/, '')\n\t\t\t\t: new URLSearchParams(this._options.searchParams as unknown as SearchParamsInit).toString();\n\t\t\t// eslint-disable-next-line unicorn/prevent-abbreviations\n\t\t\tconst searchParams = '?' + textSearchParams;\n\t\t\tconst url = this.request.url.replace(/(?:\\?.*?)?(?=#|$)/, searchParams);\n\n\t\t\t// To provide correct form boundary, Content-Type header should be deleted each time when new Request instantiated from another one\n\t\t\tif (\n\t\t\t\t((supportsFormData && this._options.body instanceof globalThis.FormData)\n\t\t\t\t\t|| this._options.body instanceof URLSearchParams) && !(this._options.headers && (this._options.headers as Record<string, string>)['content-type'])\n\t\t\t) {\n\t\t\t\tthis.request.headers.delete('content-type');\n\t\t\t}\n\n\t\t\t// The spread of `this.request` is required as otherwise it misses the `duplex` option for some reason and throws.\n\t\t\tthis.request = new globalThis.Request(new globalThis.Request(url, {...this.request}), this._options as RequestInit);\n\t\t}\n\n\t\t// If `onUploadProgress` is passed, it uses the stream API internally\n\t\tif (this._options.onUploadProgress) {\n\t\t\tif (typeof this._options.onUploadProgress !== 'function') {\n\t\t\t\tthrow new TypeError('The `onUploadProgress` option must be a function');\n\t\t\t}\n\n\t\t\tif (!supportsRequestStreams) {\n\t\t\t\tthrow new Error('Request streams are not supported in your environment. The `duplex` option for `Request` is not available.');\n\t\t\t}\n\n\t\t\tconst originalBody = this.request.body;\n\t\t\tif (originalBody) {\n\t\t\t\tthis.request = streamRequest(this.request, this._options.onUploadProgress);\n\t\t\t}\n\t\t}\n\t}\n\n\tprotected _calculateRetryDelay(error: unknown) {\n\t\tthis._retryCount++;\n\n\t\tif (this._retryCount > this._options.retry.limit || error instanceof TimeoutError) {\n\t\t\tthrow error;\n\t\t}\n\n\t\tif (error instanceof HTTPError) {\n\t\t\tif (!this._options.retry.statusCodes.includes(error.response.status)) {\n\t\t\t\tthrow error;\n\t\t\t}\n\n\t\t\tconst retryAfter = error.response.headers.get('Retry-After')\n\t\t\t\t?? error.response.headers.get('RateLimit-Reset')\n\t\t\t\t?? error.response.headers.get('X-RateLimit-Reset') // GitHub\n\t\t\t\t?? error.response.headers.get('X-Rate-Limit-Reset'); // Twitter\n\t\t\tif (retryAfter && this._options.retry.afterStatusCodes.includes(error.response.status)) {\n\t\t\t\tlet after = Number(retryAfter) * 1000;\n\t\t\t\tif (Number.isNaN(after)) {\n\t\t\t\t\tafter = Date.parse(retryAfter) - Date.now();\n\t\t\t\t} else if (after >= Date.parse('2024-01-01')) {\n\t\t\t\t\t// A large number is treated as a timestamp (fixed threshold protects against clock skew)\n\t\t\t\t\tafter -= Date.now();\n\t\t\t\t}\n\n\t\t\t\tconst max = this._options.retry.maxRetryAfter ?? after;\n\t\t\t\treturn after < max ? after : max;\n\t\t\t}\n\n\t\t\tif (error.response.status === 413) {\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\n\t\tconst retryDelay = this._options.retry.delay(this._retryCount);\n\t\treturn Math.min(this._options.retry.backoffLimit, retryDelay);\n\t}\n\n\tprotected _decorateResponse(response: Response): Response {\n\t\tif (this._options.parseJson) {\n\t\t\tresponse.json = async () => this._options.parseJson!(await response.text());\n\t\t}\n\n\t\treturn response;\n\t}\n\n\tprotected async _retry<T extends (...arguments_: any) => Promise<any>>(function_: T): Promise<ReturnType<T> | void> {\n\t\ttry {\n\t\t\treturn await function_();\n\t\t} catch (error) {\n\t\t\tconst ms = Math.min(this._calculateRetryDelay(error), maxSafeTimeout);\n\t\t\tif (this._retryCount < 1) {\n\t\t\t\tthrow error;\n\t\t\t}\n\n\t\t\tawait delay(ms, {signal: this._options.signal});\n\n\t\t\tfor (const hook of this._options.hooks.beforeRetry) {\n\t\t\t\t// eslint-disable-next-line no-await-in-loop\n\t\t\t\tconst hookResult = await hook({\n\t\t\t\t\trequest: this.request,\n\t\t\t\t\toptions: (this._options as unknown) as NormalizedOptions,\n\t\t\t\t\terror: error as Error,\n\t\t\t\t\tretryCount: this._retryCount,\n\t\t\t\t});\n\n\t\t\t\t// If `stop` is returned from the hook, the retry process is stopped\n\t\t\t\tif (hookResult === stop) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn this._retry(function_);\n\t\t}\n\t}\n\n\tprotected async _fetch(): Promise<Response> {\n\t\tfor (const hook of this._options.hooks.beforeRequest) {\n\t\t\t// eslint-disable-next-line no-await-in-loop\n\t\t\tconst result = await hook(this.request, (this._options as unknown) as NormalizedOptions);\n\n\t\t\tif (result instanceof Request) {\n\t\t\t\tthis.request = result;\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tif (result instanceof Response) {\n\t\t\t\treturn result;\n\t\t\t}\n\t\t}\n\n\t\tconst nonRequestOptions = findUnknownOptions(this.request, this._options);\n\n\t\t// Cloning is done here to prepare in advance for retries\n\t\tconst mainRequest = this.request;\n\t\tthis.request = mainRequest.clone();\n\n\t\tif (this._options.timeout === false) {\n\t\t\treturn this._options.fetch(mainRequest, nonRequestOptions);\n\t\t}\n\n\t\treturn timeout(mainRequest, nonRequestOptions, this.abortController, this._options as TimeoutOptions);\n\t}\n}\n", "/*! MIT License © Sindre Sorhus */\n\nimport {Ky} from './core/Ky.js';\nimport {requestMethods, stop} from './core/constants.js';\nimport type {KyInstance} from './types/ky.js';\nimport type {Input, Options} from './types/options.js';\nimport {validateAndMerge} from './utils/merge.js';\nimport {type Mutable} from './utils/types.js';\n\nconst createInstance = (defaults?: Partial<Options>): KyInstance => {\n\t// eslint-disable-next-line @typescript-eslint/promise-function-async\n\tconst ky: Partial<Mutable<KyInstance>> = (input: Input, options?: Options) => Ky.create(input, validateAndMerge(defaults, options));\n\n\tfor (const method of requestMethods) {\n\t\t// eslint-disable-next-line @typescript-eslint/promise-function-async\n\t\tky[method] = (input: Input, options?: Options) => Ky.create(input, validateAndMerge(defaults, options, {method}));\n\t}\n\n\tky.create = (newDefaults?: Partial<Options>) => createInstance(validateAndMerge(newDefaults));\n\tky.extend = (newDefaults?: Partial<Options> | ((parentDefaults: Partial<Options>) => Partial<Options>)) => {\n\t\tif (typeof newDefaults === 'function') {\n\t\t\tnewDefaults = newDefaults(defaults ?? {});\n\t\t}\n\n\t\treturn createInstance(validateAndMerge(defaults, newDefaults));\n\t};\n\n\tky.stop = stop;\n\n\treturn ky as KyInstance;\n};\n\nconst ky = createInstance();\n\nexport default ky;\n\nexport type {KyInstance} from './types/ky.js';\n\nexport type {\n\tInput,\n\tOptions,\n\tNormalizedOptions,\n\tRetryOptions,\n\tSearchParamsOption,\n\tProgress,\n} from './types/options.js';\n\nexport type {\n\tHooks,\n\tBeforeRequestHook,\n\tBeforeRetryHook,\n\tBeforeRetryState,\n\tBeforeErrorHook,\n\tAfterResponseHook,\n} from './types/hooks.js';\n\nexport type {ResponsePromise} from './types/ResponsePromise.js';\nexport type {KyRequest} from './types/request.js';\nexport type {KyResponse} from './types/response.js';\nexport {HTTPError} from './errors/HTTPError.js';\nexport {TimeoutError} from './errors/TimeoutError.js';\n"], "mappings": ";;;;;AAIM,IAAO,YAAP,cAAsC,MAAK;EAKhD,YAAY,UAAoB,SAAkB,SAA0B;AAC3E,UAAM,OAAQ,SAAS,UAAU,SAAS,WAAW,IAAK,SAAS,SAAS;AAC5E,UAAM,QAAQ,SAAS,cAAc;AACrC,UAAM,SAAS,GAAG,IAAI,IAAI,KAAK,GAAG,KAAI;AACtC,UAAM,SAAS,SAAS,eAAe,MAAM,KAAK;AAElD,UAAM,uBAAuB,MAAM,KAAK,QAAQ,MAAM,IAAI,QAAQ,GAAG,EAAE;AAVjE;AACA;AACA;AAUN,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,UAAU;EAChB;;;;ACnBK,IAAO,eAAP,cAA4B,MAAK;EAGtC,YAAY,SAAgB;AAC3B,UAAM,sBAAsB,QAAQ,MAAM,IAAI,QAAQ,GAAG,EAAE;AAHrD;AAIN,SAAK,OAAO;AACZ,SAAK,UAAU;EAChB;;;;ACLM,IAAM,0BAA0B,MAAK;AAC3C,MAAI,iBAAiB;AACrB,MAAI,iBAAiB;AACrB,QAAM,yBAAyB,OAAO,WAAW,mBAAmB;AACpE,QAAM,kBAAkB,OAAO,WAAW,YAAY;AAEtD,MAAI,0BAA0B,iBAAiB;AAC9C,QAAI;AACH,uBAAiB,IAAI,WAAW,QAAQ,yBAAyB;QAChE,MAAM,IAAI,WAAW,eAAc;QACnC,QAAQ;;QAER,IAAI,SAAM;AACT,2BAAiB;AACjB,iBAAO;QACR;OACA,EAAE,QAAQ,IAAI,cAAc;IAC9B,SAAS,OAAO;AAEf,UAAI,iBAAiB,SAAS,MAAM,YAAY,6BAA6B;AAC5E,eAAO;MACR;AAEA,YAAM;IACP;EACD;AAEA,SAAO,kBAAkB,CAAC;AAC3B,GAAE;AAEK,IAAM,0BAA0B,OAAO,WAAW,oBAAoB;AACtE,IAAM,0BAA0B,OAAO,WAAW,mBAAmB;AACrE,IAAM,mBAAmB,OAAO,WAAW,aAAa;AAExD,IAAM,iBAAiB,CAAC,OAAO,QAAQ,OAAO,SAAS,QAAQ,QAAQ;AAE9E,IAAM,WAAW,MAA6B;AAC9C,SAAQ;AAID,IAAM,gBAAgB;EAC5B,MAAM;EACN,MAAM;EACN,UAAU;EACV,aAAa;EACb,MAAM;;AAIA,IAAM,iBAAiB;AAGvB,IAAM,wBAAwB,IAAI,YAAW,EAAG,OAAO,0CAA0C,EAAE;AAEnG,IAAM,OAAO,OAAO,MAAM;AAE1B,IAAM,eAAkC;EAC9C,MAAM;EACN,WAAW;EACX,eAAe;EACf,cAAc;EACd,WAAW;EACX,OAAO;EACP,SAAS;EACT,OAAO;EACP,iBAAiB;EACjB,oBAAoB;EACpB,kBAAkB;EAClB,OAAO;;AAGD,IAAM,yBAA8C;EAC1D,QAAQ;EACR,SAAS;EACT,MAAM;EACN,MAAM;EACN,aAAa;EACb,OAAO;EACP,UAAU;EACV,UAAU;EACV,gBAAgB;EAChB,WAAW;EACX,WAAW;EACX,QAAQ;EACR,QAAQ;EACR,YAAY;EACZ,QAAQ;EACR,UAAU;;;;ACxFJ,IAAM,cAAc,CAAC,SAAkC;AAC7D,MAAI,CAAC,MAAM;AACV,WAAO;EACR;AAEA,MAAI,gBAAgB,UAAU;AAE7B,QAAI,OAAO;AAEX,eAAW,CAAC,KAAK,KAAK,KAAK,MAAM;AAChC,cAAQ;AACR,cAAQ,IAAI,YAAW,EAAG,OAAO,yCAAyC,GAAG,GAAG,EAAE;AAClF,cAAQ,OAAO,UAAU,WACtB,IAAI,YAAW,EAAG,OAAO,KAAK,EAAE,SAChC,MAAM;IACV;AAEA,WAAO;EACR;AAEA,MAAI,gBAAgB,MAAM;AACzB,WAAO,KAAK;EACb;AAEA,MAAI,gBAAgB,aAAa;AAChC,WAAO,KAAK;EACb;AAEA,MAAI,OAAO,SAAS,UAAU;AAC7B,WAAO,IAAI,YAAW,EAAG,OAAO,IAAI,EAAE;EACvC;AAEA,MAAI,gBAAgB,iBAAiB;AACpC,WAAO,IAAI,YAAW,EAAG,OAAO,KAAK,SAAQ,CAAE,EAAE;EAClD;AAEA,MAAI,gBAAgB,MAAM;AACzB,WAAQ,KAAM;EACf;AAEA,MAAI,OAAO,SAAS,YAAY,SAAS,MAAM;AAC9C,QAAI;AACH,YAAM,aAAa,KAAK,UAAU,IAAI;AACtC,aAAO,IAAI,YAAW,EAAG,OAAO,UAAU,EAAE;IAC7C,QAAQ;AACP,aAAO;IACR;EACD;AAEA,SAAO;AACR;AAEO,IAAM,iBAAiB,CAAC,UAAoB,uBAAqD;AACvG,QAAM,aAAa,OAAO,SAAS,QAAQ,IAAI,gBAAgB,CAAC,KAAK;AACrE,MAAI,mBAAmB;AAEvB,MAAI,SAAS,WAAW,KAAK;AAC5B,QAAI,oBAAoB;AACvB,yBAAmB,EAAC,SAAS,GAAG,YAAY,iBAAgB,GAAG,IAAI,WAAU,CAAE;IAChF;AAEA,WAAO,IAAI,SACV,MACA;MACC,QAAQ,SAAS;MACjB,YAAY,SAAS;MACrB,SAAS,SAAS;KAClB;EAEH;AAEA,SAAO,IAAI,SACV,IAAI,eAAe;IAClB,MAAM,MAAM,YAAU;AACrB,YAAM,SAAS,SAAS,KAAM,UAAS;AAEvC,UAAI,oBAAoB;AACvB,2BAAmB,EAAC,SAAS,GAAG,kBAAkB,GAAG,WAAU,GAAG,IAAI,WAAU,CAAE;MACnF;AAEA,qBAAe,OAAI;AAClB,cAAM,EAAC,MAAM,MAAK,IAAI,MAAM,OAAO,KAAI;AACvC,YAAI,MAAM;AACT,qBAAW,MAAK;AAChB;QACD;AAEA,YAAI,oBAAoB;AACvB,8BAAoB,MAAM;AAC1B,gBAAM,UAAU,eAAe,IAAI,IAAI,mBAAmB;AAC1D,6BAAmB,EAAC,SAAS,kBAAkB,WAAU,GAAG,KAAK;QAClE;AAEA,mBAAW,QAAQ,KAAK;AACxB,cAAM,KAAI;MACX;AAEA,YAAM,KAAI;IACX;GACA,GACD;IACC,QAAQ,SAAS;IACjB,YAAY,SAAS;IACrB,SAAS,SAAS;GAClB;AAEH;AAEO,IAAM,gBAAgB,CAAC,SAAkB,qBAAiD;AAChG,QAAM,aAAa,YAAY,QAAQ,IAAI;AAC3C,MAAI,mBAAmB;AAEvB,SAAO,IAAI,QAAQ,SAAS;;IAE3B,QAAQ;IACR,MAAM,IAAI,eAAe;MACxB,MAAM,MAAM,YAAU;AACrB,cAAM,SAAS,QAAQ,gBAAgB,iBAAiB,QAAQ,KAAK,UAAS,IAAK,IAAI,SAAS,EAAE,EAAE,KAAM,UAAS;AAEnH,uBAAe,OAAI;AAClB,gBAAM,EAAC,MAAM,MAAK,IAAI,MAAM,OAAO,KAAI;AACvC,cAAI,MAAM;AAET,gBAAI,kBAAkB;AACrB,+BAAiB,EAAC,SAAS,GAAG,kBAAkB,YAAY,KAAK,IAAI,YAAY,gBAAgB,EAAC,GAAG,IAAI,WAAU,CAAE;YACtH;AAEA,uBAAW,MAAK;AAChB;UACD;AAEA,8BAAoB,MAAM;AAC1B,cAAI,UAAU,eAAe,IAAI,IAAI,mBAAmB;AACxD,cAAI,aAAa,oBAAoB,YAAY,GAAG;AACnD,sBAAU;UACX;AAEA,cAAI,kBAAkB;AACrB,6BAAiB,EAAC,SAAS,OAAO,QAAQ,QAAQ,CAAC,CAAC,GAAG,kBAAkB,WAAU,GAAG,KAAK;UAC5F;AAEA,qBAAW,QAAQ,KAAK;AACxB,gBAAM,KAAI;QACX;AAEA,cAAM,KAAI;MACX;KACA;GACD;AACF;;;ACxJO,IAAM,WAAW,CAAC,UAAoC,UAAU,QAAQ,OAAO,UAAU;;;ACGzF,IAAM,mBAAmB,IAAI,YAAkE;AACrG,aAAW,UAAU,SAAS;AAC7B,SAAK,CAAC,SAAS,MAAM,KAAK,MAAM,QAAQ,MAAM,MAAM,WAAW,QAAW;AACzE,YAAM,IAAI,UAAU,0CAA0C;IAC/D;EACD;AAEA,SAAO,UAAU,CAAA,GAAI,GAAG,OAAO;AAChC;AAEO,IAAM,eAAe,CAAC,UAAyB,CAAA,GAAI,UAAyB,CAAA,MAAM;AACxF,QAAM,SAAS,IAAI,WAAW,QAAQ,OAAiC;AACvE,QAAM,oBAAoB,mBAAmB,WAAW;AACxD,QAAM,SAAS,IAAI,WAAW,QAAQ,OAAiC;AAEvE,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAO,GAAI;AAC5C,QAAK,qBAAqB,UAAU,eAAgB,UAAU,QAAW;AACxE,aAAO,OAAO,GAAG;IAClB,OAAO;AACN,aAAO,IAAI,KAAK,KAAK;IACtB;EACD;AAEA,SAAO;AACR;AAEA,SAAS,aAAoC,UAAiB,UAAiB,UAAW;AACzF,SAAQ,OAAO,OAAO,UAAU,QAAQ,KAAK,SAAS,QAAQ,MAAM,SACjE,CAAA,IACA,UAA8B,SAAS,QAAQ,KAAK,CAAA,GAAI,SAAS,QAAQ,KAAK,CAAA,CAAE;AACpF;AAEO,IAAM,aAAa,CAAC,WAAkB,CAAA,GAAI,WAAkB,CAAA,OAClE;EACC,eAAe,aAAa,UAAU,UAAU,eAAe;EAC/D,aAAa,aAAa,UAAU,UAAU,aAAa;EAC3D,eAAe,aAAa,UAAU,UAAU,eAAe;EAC/D,aAAa,aAAa,UAAU,UAAU,aAAa;;AAKtD,IAAM,YAAY,IAAO,YAA6C;AAC5E,MAAI,cAAmB,CAAA;AACvB,MAAI,UAAU,CAAA;AACd,MAAI,QAAQ,CAAA;AAEZ,aAAW,UAAU,SAAS;AAC7B,QAAI,MAAM,QAAQ,MAAM,GAAG;AAC1B,UAAI,CAAC,MAAM,QAAQ,WAAW,GAAG;AAChC,sBAAc,CAAA;MACf;AAEA,oBAAc,CAAC,GAAG,aAAa,GAAG,MAAM;IACzC,WAAW,SAAS,MAAM,GAAG;AAC5B,eAAS,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,MAAM,GAAG;AAChD,YAAI,SAAS,KAAK,KAAK,OAAO,aAAa;AAC1C,kBAAQ,UAAU,YAAY,GAAG,GAAG,KAAK;QAC1C;AAEA,sBAAc,EAAC,GAAG,aAAa,CAAC,GAAG,GAAG,MAAK;MAC5C;AAEA,UAAI,SAAU,OAAe,KAAK,GAAG;AACpC,gBAAQ,WAAW,OAAQ,OAAe,KAAK;AAC/C,oBAAY,QAAQ;MACrB;AAEA,UAAI,SAAU,OAAe,OAAO,GAAG;AACtC,kBAAU,aAAa,SAAU,OAAe,OAAO;AACvD,oBAAY,UAAU;MACvB;IACD;EACD;AAEA,SAAO;AACR;;;AC5EO,IAAM,yBAAyB,CAAC,UACtC,eAAe,SAAS,KAAmB,IAAI,MAAM,YAAW,IAAK;AAEtE,IAAM,eAAe,CAAC,OAAO,OAAO,QAAQ,UAAU,WAAW,OAAO;AAExE,IAAM,mBAAmB,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAE3D,IAAM,wBAAwB,CAAC,KAAK,KAAK,GAAG;AAE5C,IAAM,sBAA8C;EACnD,OAAO;EACP,SAAS;EACT,aAAa;EACb,kBAAkB;EAClB,eAAe,OAAO;EACtB,cAAc,OAAO;EACrB,OAAO,kBAAgB,MAAO,MAAM,eAAe,KAAM;;AAGnD,IAAM,wBAAwB,CAAC,QAA+B,CAAA,MAA8B;AAClG,MAAI,OAAO,UAAU,UAAU;AAC9B,WAAO;MACN,GAAG;MACH,OAAO;;EAET;AAEA,MAAI,MAAM,WAAW,CAAC,MAAM,QAAQ,MAAM,OAAO,GAAG;AACnD,UAAM,IAAI,MAAM,gCAAgC;EACjD;AAEA,MAAI,MAAM,eAAe,CAAC,MAAM,QAAQ,MAAM,WAAW,GAAG;AAC3D,UAAM,IAAI,MAAM,oCAAoC;EACrD;AAEA,SAAO;IACN,GAAG;IACH,GAAG;;AAEL;;;ACnCA,eAAO,QACN,SACA,MACA,iBACA,SAAuB;AAEvB,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACtC,UAAM,YAAY,WAAW,MAAK;AACjC,UAAI,iBAAiB;AACpB,wBAAgB,MAAK;MACtB;AAEA,aAAO,IAAI,aAAa,OAAO,CAAC;IACjC,GAAG,QAAQ,OAAO;AAElB,SAAK,QACH,MAAM,SAAS,IAAI,EACnB,KAAK,OAAO,EACZ,MAAM,MAAM,EACZ,KAAK,MAAK;AACV,mBAAa,SAAS;IACvB,CAAC;EACH,CAAC;AACF;;;ACvBA,eAAO,MACN,IACA,EAAC,OAAM,GAAe;AAEtB,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACtC,QAAI,QAAQ;AACX,aAAO,eAAc;AACrB,aAAO,iBAAiB,SAAS,cAAc,EAAC,MAAM,KAAI,CAAC;IAC5D;AAEA,aAAS,eAAY;AACpB,mBAAa,SAAS;AACtB,aAAO,OAAQ,MAAe;IAC/B;AAEA,UAAM,YAAY,WAAW,MAAK;AACjC,uCAAQ,oBAAoB,SAAS;AACrC,cAAO;IACR,GAAG,EAAE;EACN,CAAC;AACF;;;AC1BO,IAAM,qBAAqB,CACjC,SACA,YAC4B;AAC5B,QAAM,iBAA0C,CAAA;AAEhD,aAAW,OAAO,SAAS;AAC1B,QAAI,EAAE,OAAO,2BAA2B,EAAE,OAAO,iBAAiB,EAAE,OAAO,UAAU;AACpF,qBAAe,GAAG,IAAI,QAAQ,GAAG;IAClC;EACD;AAEA,SAAO;AACR;;;ACYM,IAAO,KAAP,MAAO,IAAE;;EAuGd,YAAY,OAAc,UAAmB,CAAA,GAAE;AAPxC;AACG;AACA,uCAAc;AACd;AACA;AA/HX;AAmIE,SAAK,SAAS;AAEd,SAAK,WAAW;MACf,GAAG;MACH,SAAS,aAAc,KAAK,OAAmB,SAAS,QAAQ,OAAO;MACvE,OAAO,WACN;QACC,eAAe,CAAA;QACf,aAAa,CAAA;QACb,aAAa,CAAA;QACb,eAAe,CAAA;SAEhB,QAAQ,KAAK;MAEd,QAAQ,uBAAuB,QAAQ,UAAW,KAAK,OAAmB,UAAU,KAAK;;MAEzF,WAAW,OAAO,QAAQ,aAAa,EAAE;MACzC,OAAO,sBAAsB,QAAQ,KAAK;MAC1C,iBAAiB,QAAQ,oBAAoB;MAC7C,SAAS,QAAQ,WAAW;MAC5B,OAAO,QAAQ,SAAS,WAAW,MAAM,KAAK,UAAU;;AAGzD,QAAI,OAAO,KAAK,WAAW,YAAY,EAAE,KAAK,kBAAkB,OAAO,KAAK,kBAAkB,WAAW,UAAU;AAClH,YAAM,IAAI,UAAU,2CAA2C;IAChE;AAEA,QAAI,KAAK,SAAS,aAAa,OAAO,KAAK,WAAW,UAAU;AAC/D,UAAI,KAAK,OAAO,WAAW,GAAG,GAAG;AAChC,cAAM,IAAI,MAAM,4DAA4D;MAC7E;AAEA,UAAI,CAAC,KAAK,SAAS,UAAU,SAAS,GAAG,GAAG;AAC3C,aAAK,SAAS,aAAa;MAC5B;AAEA,WAAK,SAAS,KAAK,SAAS,YAAY,KAAK;IAC9C;AAEA,QAAI,yBAAyB;AAC5B,YAAM,iBAAiB,KAAK,SAAS,UAAW,KAAK,OAAmB;AACxE,WAAK,kBAAkB,IAAI,WAAW,gBAAe;AACrD,WAAK,SAAS,SAAS,iBAAiB,YAAY,IAAI,CAAC,gBAAgB,KAAK,gBAAgB,MAAM,CAAC,IAAI,KAAK,gBAAgB;IAC/H;AAEA,QAAI,wBAAwB;AAE3B,WAAK,SAAS,SAAS;IACxB;AAEA,QAAI,KAAK,SAAS,SAAS,QAAW;AACrC,WAAK,SAAS,SAAO,gBAAK,UAAS,kBAAd,4BAA8B,KAAK,SAAS,UAAS,KAAK,UAAU,KAAK,SAAS,IAAI;AAC3G,WAAK,SAAS,QAAQ,IAAI,gBAAgB,KAAK,SAAS,QAAQ,IAAI,cAAc,KAAK,kBAAkB;IAC1G;AAEA,SAAK,UAAU,IAAI,WAAW,QAAQ,KAAK,QAAQ,KAAK,QAAQ;AAEhE,QAAI,KAAK,SAAS,cAAc;AAE/B,YAAM,mBAAmB,OAAO,KAAK,SAAS,iBAAiB,WAC5D,KAAK,SAAS,aAAa,QAAQ,OAAO,EAAE,IAC5C,IAAI,gBAAgB,KAAK,SAAS,YAA2C,EAAE,SAAQ;AAE1F,YAAM,eAAe,MAAM;AAC3B,YAAM,MAAM,KAAK,QAAQ,IAAI,QAAQ,qBAAqB,YAAY;AAGtE,WACG,oBAAoB,KAAK,SAAS,gBAAgB,WAAW,YAC3D,KAAK,SAAS,gBAAgB,oBAAoB,EAAE,KAAK,SAAS,WAAY,KAAK,SAAS,QAAmC,cAAc,IAChJ;AACD,aAAK,QAAQ,QAAQ,OAAO,cAAc;MAC3C;AAGA,WAAK,UAAU,IAAI,WAAW,QAAQ,IAAI,WAAW,QAAQ,KAAK,EAAC,GAAG,KAAK,QAAO,CAAC,GAAG,KAAK,QAAuB;IACnH;AAGA,QAAI,KAAK,SAAS,kBAAkB;AACnC,UAAI,OAAO,KAAK,SAAS,qBAAqB,YAAY;AACzD,cAAM,IAAI,UAAU,kDAAkD;MACvE;AAEA,UAAI,CAAC,wBAAwB;AAC5B,cAAM,IAAI,MAAM,4GAA4G;MAC7H;AAEA,YAAM,eAAe,KAAK,QAAQ;AAClC,UAAI,cAAc;AACjB,aAAK,UAAU,cAAc,KAAK,SAAS,KAAK,SAAS,gBAAgB;MAC1E;IACD;EACD;EApMA,OAAO,OAAO,OAAc,SAAgB;AAC3C,UAAMA,MAAK,IAAI,IAAG,OAAO,OAAO;AAEhC,UAAM,YAAY,YAA8B;AAC/C,UAAI,OAAOA,IAAG,SAAS,YAAY,YAAYA,IAAG,SAAS,UAAU,gBAAgB;AACpF,cAAM,IAAI,WAAW,iDAAiD,cAAc,EAAE;MACvF;AAGA,YAAM,QAAQ,QAAO;AAGrB,UAAI,WAAW,MAAMA,IAAG,OAAM;AAE9B,iBAAW,QAAQA,IAAG,SAAS,MAAM,eAAe;AAEnD,cAAM,mBAAmB,MAAM,KAC9BA,IAAG,SACHA,IAAG,UACHA,IAAG,kBAAkB,SAAS,MAAK,CAAE,CAAC;AAGvC,YAAI,4BAA4B,WAAW,UAAU;AACpD,qBAAW;QACZ;MACD;AAEA,MAAAA,IAAG,kBAAkB,QAAQ;AAE7B,UAAI,CAAC,SAAS,MAAMA,IAAG,SAAS,iBAAiB;AAChD,YAAI,QAAQ,IAAI,UAAU,UAAUA,IAAG,SAASA,IAAG,QAA6B;AAEhF,mBAAW,QAAQA,IAAG,SAAS,MAAM,aAAa;AAEjD,kBAAQ,MAAM,KAAK,KAAK;QACzB;AAEA,cAAM;MACP;AAGA,UAAIA,IAAG,SAAS,oBAAoB;AACnC,YAAI,OAAOA,IAAG,SAAS,uBAAuB,YAAY;AACzD,gBAAM,IAAI,UAAU,oDAAoD;QACzE;AAEA,YAAI,CAAC,yBAAyB;AAC7B,gBAAM,IAAI,MAAM,6EAA6E;QAC9F;AAEA,eAAO,eAAe,SAAS,MAAK,GAAIA,IAAG,SAAS,kBAAkB;MACvE;AAEA,aAAO;IACR;AAEA,UAAM,oBAAoBA,IAAG,SAAS,MAAM,QAAQ,SAASA,IAAG,QAAQ,OAAO,YAAW,CAAE;AAC5F,UAAM,UAAU,oBAAoBA,IAAG,OAAO,SAAS,IAAI,UAAS,GAClE,QAAQ,YAAW;AAtFvB;AAwFI,UAAI,CAACA,IAAG,QAAQ,UAAU;AACzB,gBAAM,KAAAA,IAAG,QAAQ,SAAX,mBAAiB;MACxB;IACD,CAAC;AAEF,eAAW,CAAC,MAAM,QAAQ,KAAK,OAAO,QAAQ,aAAa,GAA0C;AACpG,aAAO,IAAI,IAAI,YAAW;AAEzB,QAAAA,IAAG,QAAQ,QAAQ,IAAI,UAAUA,IAAG,QAAQ,QAAQ,IAAI,QAAQ,KAAK,QAAQ;AAE7E,cAAM,WAAW,MAAM;AAEvB,YAAI,SAAS,QAAQ;AACpB,cAAI,SAAS,WAAW,KAAK;AAC5B,mBAAO;UACR;AAEA,gBAAM,cAAc,MAAM,SAAS,MAAK,EAAG,YAAW;AACtD,gBAAM,eAAe,YAAY;AACjC,cAAI,iBAAiB,GAAG;AACvB,mBAAO;UACR;AAEA,cAAI,QAAQ,WAAW;AACtB,mBAAO,QAAQ,UAAU,MAAM,SAAS,KAAI,CAAE;UAC/C;QACD;AAEA,eAAO,SAAS,IAAI,EAAC;MACtB;IACD;AAEA,WAAO;EACR;EAyGU,qBAAqB,OAAc;AAC5C,SAAK;AAEL,QAAI,KAAK,cAAc,KAAK,SAAS,MAAM,SAAS,iBAAiB,cAAc;AAClF,YAAM;IACP;AAEA,QAAI,iBAAiB,WAAW;AAC/B,UAAI,CAAC,KAAK,SAAS,MAAM,YAAY,SAAS,MAAM,SAAS,MAAM,GAAG;AACrE,cAAM;MACP;AAEA,YAAM,aAAa,MAAM,SAAS,QAAQ,IAAI,aAAa,KACvD,MAAM,SAAS,QAAQ,IAAI,iBAAiB,KAC5C,MAAM,SAAS,QAAQ,IAAI,mBAAmB,KAC9C,MAAM,SAAS,QAAQ,IAAI,oBAAoB;AACnD,UAAI,cAAc,KAAK,SAAS,MAAM,iBAAiB,SAAS,MAAM,SAAS,MAAM,GAAG;AACvF,YAAI,QAAQ,OAAO,UAAU,IAAI;AACjC,YAAI,OAAO,MAAM,KAAK,GAAG;AACxB,kBAAQ,KAAK,MAAM,UAAU,IAAI,KAAK,IAAG;QAC1C,WAAW,SAAS,KAAK,MAAM,YAAY,GAAG;AAE7C,mBAAS,KAAK,IAAG;QAClB;AAEA,cAAM,MAAM,KAAK,SAAS,MAAM,iBAAiB;AACjD,eAAO,QAAQ,MAAM,QAAQ;MAC9B;AAEA,UAAI,MAAM,SAAS,WAAW,KAAK;AAClC,cAAM;MACP;IACD;AAEA,UAAM,aAAa,KAAK,SAAS,MAAM,MAAM,KAAK,WAAW;AAC7D,WAAO,KAAK,IAAI,KAAK,SAAS,MAAM,cAAc,UAAU;EAC7D;EAEU,kBAAkB,UAAkB;AAC7C,QAAI,KAAK,SAAS,WAAW;AAC5B,eAAS,OAAO,YAAY,KAAK,SAAS,UAAW,MAAM,SAAS,KAAI,CAAE;IAC3E;AAEA,WAAO;EACR;EAEU,MAAM,OAAuD,WAAY;AAClF,QAAI;AACH,aAAO,MAAM,UAAS;IACvB,SAAS,OAAO;AACf,YAAM,KAAK,KAAK,IAAI,KAAK,qBAAqB,KAAK,GAAG,cAAc;AACpE,UAAI,KAAK,cAAc,GAAG;AACzB,cAAM;MACP;AAEA,YAAM,MAAM,IAAI,EAAC,QAAQ,KAAK,SAAS,OAAM,CAAC;AAE9C,iBAAW,QAAQ,KAAK,SAAS,MAAM,aAAa;AAEnD,cAAM,aAAa,MAAM,KAAK;UAC7B,SAAS,KAAK;UACd,SAAU,KAAK;UACf;UACA,YAAY,KAAK;SACjB;AAGD,YAAI,eAAe,MAAM;AACxB;QACD;MACD;AAEA,aAAO,KAAK,OAAO,SAAS;IAC7B;EACD;EAEU,MAAM,SAAM;AACrB,eAAW,QAAQ,KAAK,SAAS,MAAM,eAAe;AAErD,YAAM,SAAS,MAAM,KAAK,KAAK,SAAU,KAAK,QAAyC;AAEvF,UAAI,kBAAkB,SAAS;AAC9B,aAAK,UAAU;AACf;MACD;AAEA,UAAI,kBAAkB,UAAU;AAC/B,eAAO;MACR;IACD;AAEA,UAAM,oBAAoB,mBAAmB,KAAK,SAAS,KAAK,QAAQ;AAGxE,UAAM,cAAc,KAAK;AACzB,SAAK,UAAU,YAAY,MAAK;AAEhC,QAAI,KAAK,SAAS,YAAY,OAAO;AACpC,aAAO,KAAK,SAAS,MAAM,aAAa,iBAAiB;IAC1D;AAEA,WAAO,QAAQ,aAAa,mBAAmB,KAAK,iBAAiB,KAAK,QAA0B;EACrG;;;;AC/TD,IAAM,iBAAiB,CAAC,aAA2C;AAElE,QAAMC,MAAmC,CAAC,OAAc,YAAsB,GAAG,OAAO,OAAO,iBAAiB,UAAU,OAAO,CAAC;AAElI,aAAW,UAAU,gBAAgB;AAEpC,IAAAA,IAAG,MAAM,IAAI,CAAC,OAAc,YAAsB,GAAG,OAAO,OAAO,iBAAiB,UAAU,SAAS,EAAC,OAAM,CAAC,CAAC;EACjH;AAEA,EAAAA,IAAG,SAAS,CAAC,gBAAmC,eAAe,iBAAiB,WAAW,CAAC;AAC5F,EAAAA,IAAG,SAAS,CAAC,gBAA6F;AACzG,QAAI,OAAO,gBAAgB,YAAY;AACtC,oBAAc,YAAY,YAAY,CAAA,CAAE;IACzC;AAEA,WAAO,eAAe,iBAAiB,UAAU,WAAW,CAAC;EAC9D;AAEA,EAAAA,IAAG,OAAO;AAEV,SAAOA;AACR;AAEA,IAAM,KAAK,eAAc;AAEzB,IAAA,uBAAe;", "names": ["ky", "ky"]}