"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.databaseProviders = exports.DATABASE_CONNECTION = void 0;
const kysely_1 = require("kysely");
const mariadb_1 = require("mariadb");
const config_1 = require("@nestjs/config");
exports.DATABASE_CONNECTION = 'DATABASE_CONNECTION';
exports.databaseProviders = [
    {
        provide: exports.DATABASE_CONNECTION,
        useFactory: (configService) => {
            const dialect = new kysely_1.MysqlDialect({
                pool: (0, mariadb_1.createPool)({
                    host: configService.get('DB_HOST') || 'localhost',
                    port: configService.get('DB_PORT') || 3306,
                    user: configService.get('DB_USER') || 'root',
                    password: configService.get('DB_PASSWORD'),
                    database: configService.get('DB_DATABASE'),
                    connectionLimit: 10,
                }),
            });
            return new kysely_1.Kysely({
                dialect,
            });
        },
        inject: [config_1.ConfigService],
    },
];
//# sourceMappingURL=database.provider.js.map