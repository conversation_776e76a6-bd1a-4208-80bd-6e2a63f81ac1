#!/usr/bin/env node

// Teste da nova implementação MCP
const SERVER_URL = 'http://localhost:3001/mcp';

console.log('🧪 Testando nova implementação MCP...');

// Simular uma requisição MCP initialize
const mcpInitialize = {
  "jsonrpc": "2.0",
  "id": 1,
  "method": "initialize",
  "params": {
    "protocolVersion": "2024-11-05",
    "capabilities": {
      "tools": {}
    },
    "clientInfo": {
      "name": "Test Client",
      "version": "1.0.0"
    }
  }
};

console.log('\n📤 Enviando initialize request...');

try {
  const response = await fetch(SERVER_URL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'text/event-stream',
      'Cache-Control': 'no-cache'
    },
    body: JSON.stringify(mcpInitialize)
  });

  console.log('Response Status:', response.status);
  console.log('Content-Type:', response.headers.get('content-type'));
  
  if (!response.ok) {
    console.log('❌ Response not OK');
    const text = await response.text();
    console.log('Response body:', text);
    process.exit(1);
  }

  console.log('\n📊 Lendo resposta SSE...');
  
  // Ler stream
  const reader = response.body.getReader();
  const decoder = new TextDecoder();
  
  let buffer = '';
  let messageCount = 0;
  let initializeReceived = false;
  
  // Timeout para teste
  const timeout = setTimeout(() => {
    console.log('⏰ Timeout após 10 segundos');
    if (initializeReceived) {
      console.log('✅ Initialize funcionou corretamente!');
      process.exit(0);
    } else {
      console.log('❌ Initialize não funcionou');
      process.exit(1);
    }
  }, 10000);

  while (true) {
    const { done, value } = await reader.read();
    
    if (done) break;
    
    buffer += decoder.decode(value, { stream: true });
    
    // Processar linhas SSE
    const lines = buffer.split('\n');
    buffer = lines.pop() || '';
    
    for (const line of lines) {
      if (line.trim() && line.startsWith('data: ')) {
        messageCount++;
        const data = line.replace('data: ', '');
        console.log(`📨 Message ${messageCount}:`, data);
        
        try {
          const parsed = JSON.parse(data);
          if (parsed.jsonrpc === '2.0' && parsed.result) {
            console.log('✅ Received valid MCP response!');
            initializeReceived = true;
            
            // Teste tools/list
            await testToolsList();
            clearTimeout(timeout);
            process.exit(0);
          }
        } catch (e) {
          // Pode ser uma mensagem de inicialização ou keepalive
          console.log('📋 Non-JSON message:', data);
        }
      }
    }
  }
  
  clearTimeout(timeout);
  
} catch (error) {
  console.error('❌ Error:', error.message);
  process.exit(1);
}

async function testToolsList() {
  console.log('\n🔧 Testando tools/list...');
  
  const toolsListRequest = {
    "jsonrpc": "2.0",
    "id": 2,
    "method": "tools/list",
    "params": {}
  };
  
  try {
    const response = await fetch(SERVER_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache'
      },
      body: JSON.stringify(toolsListRequest)
    });

    if (!response.ok) {
      console.log('❌ Tools list request failed');
      return;
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    
    let buffer = '';
    let toolsReceived = false;
    
    const timeout = setTimeout(() => {
      if (toolsReceived) {
        console.log('✅ Tools list funcionou!');
      } else {
        console.log('❌ Tools list não funcionou');
      }
    }, 5000);

    while (true) {
      const { done, value } = await reader.read();
      
      if (done) break;
      
      buffer += decoder.decode(value, { stream: true });
      
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      for (const line of lines) {
        if (line.trim() && line.startsWith('data: ')) {
          const data = line.replace('data: ', '');
          
          try {
            const parsed = JSON.parse(data);
            if (parsed.jsonrpc === '2.0' && parsed.result && parsed.result.tools) {
              console.log('✅ Tools list received!');
              console.log(`📋 Found ${parsed.result.tools.length} tools`);
              toolsReceived = true;
              clearTimeout(timeout);
              return;
            }
          } catch (e) {
            // Ignorar mensagens não-JSON
          }
        }
      }
    }
    
    clearTimeout(timeout);
    
  } catch (error) {
    console.error('❌ Error testing tools/list:', error.message);
  }
}

console.log('\n🎯 Teste concluído');
