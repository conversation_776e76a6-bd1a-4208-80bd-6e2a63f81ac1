import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

@Injectable()
export class DevAwareAuthGuard extends AuthGuard('jwt') implements CanActivate {
  canActivate(context: ExecutionContext) {
    const request = context.switchToHttp().getRequest();
    
    // Em modo de desenvolvimento, permitir bypass
    if (process.env.NODE_ENV === 'development' && process.env.DEV_MODE_ENABLED === 'true') {
      const devMode = request.headers['x-dev-mode'];
      const devUserId = request.headers['x-dev-user-id'];
      
      if (devMode === 'true' && devUserId) {
        // Simular usuário autenticado
        request.user = {
          userId: parseInt(devUserId),
          email: '<EMAIL>'
        };
        return true;
      }
    }
    
    // Usar autenticação JWT normal
    return super.canActivate(context);
  }
}
