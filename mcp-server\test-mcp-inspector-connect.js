#!/usr/bin/env node

// Teste específico para MCP Inspector connect
const SERVER_URL = 'http://localhost:3001/mcp';

console.log('🔍 Testando MCP Inspector Connect...');

async function testMCPInspectorConnect() {
  console.log('\n📡 Simulando click em Connect no MCP Inspector...');
  
  try {
    // MCP Inspector primeiro faz um GET para estabelecer a conexão
    console.log('📡 Fazendo GET para estabelecer conexão SSE...');
    
    const response = await fetch(SERVER_URL, {
      method: 'GET',
      headers: {
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive'
      }
    });

    console.log('Response Status:', response.status);
    console.log('Content-Type:', response.headers.get('content-type'));
    
    if (!response.ok) {
      console.log('❌ Falha no GET inicial');
      return;
    }

    console.log('✅ Conexão SSE estabelecida via GET');

    // Agora simular o que o MCP Inspector faz:
    // 1. Lê a resposta inicial
    // 2. Captura o endpoint da sessão (se houver)
    // 3. Envia initialize
    
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    
    let buffer = '';
    let receivedInitialData = false;
    
    // Timeout para aguardar dados iniciais
    const timeout = setTimeout(async () => {
      if (!receivedInitialData) {
        console.log('⏰ Timeout aguardando dados iniciais, tentando initialize...');
        await sendInitializeToSession();
      }
    }, 2000);

    while (true) {
      const { done, value } = await reader.read();
      
      if (done) {
        console.log('📡 Stream ended');
        break;
      }
      
      buffer += decoder.decode(value, { stream: true });
      
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      for (const line of lines) {
        if (line.trim()) {
          console.log('📨 Initial data:', line);
          receivedInitialData = true;
          
          // Se recebeu endpoint da sessão, usar ele
          if (line.includes('data: /mcp?sessionId=')) {
            const sessionEndpoint = line.replace('data: ', '');
            console.log('🔗 Session endpoint found:', sessionEndpoint);
            clearTimeout(timeout);
            await testSessionEndpoint(sessionEndpoint);
            return;
          }
          
          // Se recebeu dados iniciais mas não é endpoint, tentar initialize
          if (line.includes('event:') || line.includes('data:')) {
            clearTimeout(timeout);
            await sendInitializeToSession();
            return;
          }
        }
      }
    }
    
    clearTimeout(timeout);
    
  } catch (error) {
    console.error('❌ Erro no connect:', error.message);
  }
}

async function sendInitializeToSession() {
  console.log('\n🔄 Enviando initialize para a sessão atual...');
  
  const initializeMessage = {
    jsonrpc: '2.0',
    id: 1,
    method: 'initialize',
    params: {
      protocolVersion: '2024-11-05',
      capabilities: {
        tools: {}
      },
      clientInfo: {
        name: 'MCP Inspector',
        version: '1.0.0'
      }
    }
  };

  try {
    const response = await fetch(SERVER_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache'
      },
      body: JSON.stringify(initializeMessage)
    });

    console.log('Initialize response status:', response.status);
    
    if (!response.ok) {
      console.log('❌ Initialize falhou');
      return;
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    
    let buffer = '';
    let initializeReceived = false;
    
    const timeout = setTimeout(() => {
      if (!initializeReceived) {
        console.log('❌ Timeout aguardando initialize response');
      }
    }, 5000);

    while (true) {
      const { done, value } = await reader.read();
      
      if (done) break;
      
      buffer += decoder.decode(value, { stream: true });
      
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      for (const line of lines) {
        if (line.trim()) {
          console.log('📨 Initialize message:', line);
          
          if (line.includes('data: /mcp?sessionId=')) {
            const sessionEndpoint = line.replace('data: ', '');
            console.log('🔗 New session endpoint:', sessionEndpoint);
            clearTimeout(timeout);
            await testSessionEndpoint(sessionEndpoint);
            return;
          }
          
          if (line.startsWith('data: ')) {
            const data = line.replace('data: ', '');
            try {
              const parsed = JSON.parse(data);
              
              if (parsed.jsonrpc === '2.0' && parsed.id === 1 && parsed.result) {
                console.log('✅ Initialize response received!');
                initializeReceived = true;
                clearTimeout(timeout);
                
                // Agora enviar tools/list
                await sendToolsListToSession();
                return;
              }
            } catch (e) {
              // Ignorar mensagens não-JSON
            }
          }
        }
      }
    }
    
    clearTimeout(timeout);
    
  } catch (error) {
    console.error('❌ Erro no initialize:', error.message);
  }
}

async function testSessionEndpoint(sessionEndpoint) {
  console.log(`\\n🔗 Testando endpoint da sessão: ${sessionEndpoint}`);
  
  const sessionUrl = `http://localhost:3001${sessionEndpoint}`;
  console.log('Session URL:', sessionUrl);
  
  try {
    const response = await fetch(sessionUrl, {
      method: 'GET',
      headers: {
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache'
      }
    });

    console.log('Session response status:', response.status);
    
    if (!response.ok) {
      console.log('❌ Session endpoint falhou');
      return;
    }

    console.log('✅ Session endpoint conectado');
    
    // Agora enviar initialize para a sessão
    const initializeMessage = {
      jsonrpc: '2.0',
      id: 1,
      method: 'initialize',
      params: {
        protocolVersion: '2024-11-05',
        capabilities: {
          tools: {}
        },
        clientInfo: {
          name: 'MCP Inspector',
          version: '1.0.0'
        }
      }
    };

    const initResponse = await fetch(sessionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache'
      },
      body: JSON.stringify(initializeMessage)
    });

    console.log('Session initialize status:', initResponse.status);
    
    if (initResponse.ok) {
      console.log('✅ Session initialize enviado');
    } else {
      console.log('❌ Session initialize falhou');
    }
    
  } catch (error) {
    console.error('❌ Erro na sessão:', error.message);
  }
}

async function sendToolsListToSession() {
  console.log('\\n🔧 Enviando tools/list...');
  
  const toolsListMessage = {
    jsonrpc: '2.0',
    id: 2,
    method: 'tools/list',
    params: {}
  };

  try {
    const response = await fetch(SERVER_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache'
      },
      body: JSON.stringify(toolsListMessage)
    });

    console.log('Tools list response status:', response.status);
    
    if (!response.ok) {
      console.log('❌ Tools list falhou');
      return;
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    
    let buffer = '';
    let toolsReceived = false;
    
    const timeout = setTimeout(() => {
      if (!toolsReceived) {
        console.log('❌ Timeout aguardando tools/list response');
      }
    }, 5000);

    while (true) {
      const { done, value } = await reader.read();
      
      if (done) break;
      
      buffer += decoder.decode(value, { stream: true });
      
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      for (const line of lines) {
        if (line.trim() && line.startsWith('data: ')) {
          const data = line.replace('data: ', '');
          console.log('📨 Tools response:', data);
          
          try {
            const parsed = JSON.parse(data);
            
            if (parsed.jsonrpc === '2.0' && parsed.id === 2 && parsed.result) {
              console.log('✅ Tools list response received!');
              
              if (parsed.result.tools) {
                console.log(`📋 Found ${parsed.result.tools.length} tools:`);
                parsed.result.tools.forEach((tool, index) => {
                  console.log(`  ${index + 1}. ${tool.name} - ${tool.description}`);
                });
                
                toolsReceived = true;
                clearTimeout(timeout);
                
                console.log('\\n🎉 MCP Inspector connect simulation successful!');
                console.log('\\n💡 O MCP Inspector deveria listar as tools agora!');
                process.exit(0);
              }
            }
          } catch (e) {
            // Ignorar mensagens não-JSON
          }
        }
      }
    }
    
    clearTimeout(timeout);
    
  } catch (error) {
    console.error('❌ Erro no tools/list:', error.message);
  }
}

// Executar teste
testMCPInspectorConnect();
