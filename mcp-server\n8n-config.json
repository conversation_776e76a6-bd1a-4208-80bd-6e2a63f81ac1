{"mcp_server": {"name": "Dupli MCP Server", "type": "sse_endpoint", "url": "http://localhost:3001/mcp", "environment_variables": {"API_BASE_URL": "http://localhost:3000", "N8N_API_KEY": "your-secret-api-key-here"}}, "tools": {"integration": ["check_integration", "get_dashboard"], "tasks": ["create_task", "list_tasks", "get_task", "update_task", "delete_task", "complete_task", "list_task_categories", "create_task_category"], "finances": ["create_finance", "list_finances", "get_finance_summary", "list_finance_categories"], "ideas": ["create_idea", "list_ideas", "toggle_idea_favorite", "list_idea_categories"]}, "examples": {"list_tasks_with_filters": {"tool": "list_tasks", "args": {"phone": "5511999999999", "search": "reunião", "completed": false, "limit": 5}}, "list_finances_current_month": {"tool": "list_finances", "args": {"phone": "5511999999999", "transaction_type": "expense", "start_date": "2025-07-01T00:00:00.000Z", "end_date": "2025-07-31T23:59:59.999Z"}}, "list_favorite_ideas": {"tool": "list_ideas", "args": {"phone": "5511999999999", "is_favorite": true, "search": "app"}}}}