import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, Calendar, ChevronLeft, ChevronRight, Clock, CheckCircle, AlertCircle, Search, Filter, Grid, List, Eye } from 'lucide-react';
import Header from '../components/Header';
import ProgressCircle from '../components/ProgressCircle';
import AddTaskModal from '../components/AddTaskModal';
import { motion, AnimatePresence } from 'framer-motion';
import { useTasks, useCompleteTask, useDeleteTask, useTaskCategories } from '../hooks/useTasks';
import { TaskResponseDto } from '../types/api';

type ViewMode = 'list' | 'grid';
type FilterType = 'all' | 'appointments' | 'tasks' | 'completed' | 'pending';

const TasksPage: React.FC = () => {
  const navigate = useNavigate();
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<FilterType>('all');
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [viewMode, setViewMode] = useState<ViewMode>('list');

  // Fetch tasks and categories
  const { data: tasksData, isLoading: tasksLoading, error: tasksError } = useTasks();
  const { data: categories } = useTaskCategories();
  const completeTaskMutation = useCompleteTask();
  const deleteTaskMutation = useDeleteTask();

  // Loading state
  if (tasksLoading) {
    return (
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando tarefas...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (tasksError) {
    return (
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertCircle className="h-8 w-8 text-red-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Erro ao carregar tarefas</h2>
          <p className="text-gray-600 mb-4">
            Não foi possível carregar suas tarefas. Tente novamente.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }

  const tasks = tasksData?.tasks || [];

  // Filter and search tasks
  const getFilteredTasks = () => {
    let filteredTasks = tasks;

    // Filter by search term
    if (searchTerm) {
      filteredTasks = filteredTasks.filter(task =>
        task.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (task.description && task.description.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Filter by category
    if (selectedCategory) {
      filteredTasks = filteredTasks.filter(task => task.category_id === selectedCategory);
    }

    // Filter by type/status
    switch (selectedFilter) {
      case 'appointments':
        filteredTasks = filteredTasks.filter(task => task.task_type === 'appointment');
        break;
      case 'tasks':
        filteredTasks = filteredTasks.filter(task => task.task_type === 'task');
        break;
      case 'completed':
        filteredTasks = filteredTasks.filter(task => task.completed_at);
        break;
      case 'pending':
        filteredTasks = filteredTasks.filter(task => !task.completed_at);
        break;
    }

    return filteredTasks;
  };

  // Filter tasks for the selected date
  const getTasksForDate = (date: Date) => {
    const dateString = date.toISOString().split('T')[0];
    const filteredTasks = getFilteredTasks();

    return filteredTasks.filter(task => {
      if (!task.task_date) return false;
      const taskDate = new Date(task.task_date).toISOString().split('T')[0];
      return taskDate === dateString;
    });
  };

  const tasksForSelectedDate = getTasksForDate(selectedDate);
  const completedTasks = tasksForSelectedDate.filter(task => task.completed_at).length;
  const totalTasks = tasksForSelectedDate.length;

  // Date navigation functions
  const navigateDay = (direction: 'prev' | 'next') => {
    const newDate = new Date(selectedDate);
    newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
    setSelectedDate(newDate);
  };

  const formatDateDisplay = (date: Date) => {
    const today = new Date();
    const isToday = date.toDateString() === today.toDateString();
    
    if (isToday) {
      return 'Hoje';
    }
    
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const isYesterday = date.toDateString() === yesterday.toDateString();
    
    if (isYesterday) {
      return 'Ontem';
    }
    
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const isTomorrow = date.toDateString() === tomorrow.toDateString();
    
    if (isTomorrow) {
      return 'Amanhã';
    }
    
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: '2-digit'
    });
  };

  const handleComplete = async (taskId: number) => {
    try {
      await completeTaskMutation.mutateAsync(taskId);
    } catch (error) {
      console.error('Erro ao completar tarefa:', error);
    }
  };

  const handleDelete = async (taskId: number) => {
    if (confirm('Tem certeza que deseja excluir esta tarefa?')) {
      try {
        await deleteTaskMutation.mutateAsync(taskId);
      } catch (error) {
        console.error('Erro ao deletar tarefa:', error);
      }
    }
  };

  // Group tasks by completion status
  const pendingTasks = tasksForSelectedDate.filter(task => !task.completed_at);
  const completedTasksList = tasksForSelectedDate.filter(task => task.completed_at);

  // Task Item Component
  const TaskItem: React.FC<{ task: TaskResponseDto }> = ({ task }) => {
    const isCompleted = !!task.completed_at;
    
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`bg-white rounded-xl p-4 shadow-sm border ${
          isCompleted ? 'border-green-200 bg-green-50' : 'border-gray-200'
        }`}
      >
        <div className="flex items-start gap-3">
          <button
            onClick={() => handleComplete(task.id)}
            className={`w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5 transition-colors ${
              isCompleted 
                ? 'bg-green-500 text-white' 
                : 'border-2 border-gray-300 hover:border-green-500'
            }`}
          >
            {isCompleted && <CheckCircle size={16} />}
          </button>
          
          <div className="flex-1 min-w-0">
            <h4 className={`font-medium ${isCompleted ? 'text-gray-600 line-through' : 'text-gray-900'}`}>
              {task.name}
            </h4>
            {task.description && (
              <p className={`text-sm mt-1 ${isCompleted ? 'text-gray-500 line-through' : 'text-gray-600'}`}>
                {task.description}
              </p>
            )}
            <div className="flex items-center gap-3 mt-2 text-xs text-gray-500">
              {task.task_date && (
                <div className="flex items-center gap-1">
                  <Clock size={12} />
                  <span>{new Date(task.task_date).toLocaleTimeString('pt-BR', { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                  })}</span>
                </div>
              )}
              {task.category_name && (
                <span className="px-2 py-1 bg-gray-200 rounded-full">
                  {task.category_name}
                </span>
              )}
              <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full">
                {task.task_type === 'appointment' ? 'Compromisso' : 'Tarefa'}
              </span>
            </div>
          </div>
          
          <button
            onClick={() => handleDelete(task.id)}
            className="p-2 hover:bg-red-100 rounded-full transition-colors text-red-500"
            title="Excluir tarefa"
          >
            ×
          </button>
        </div>
      </motion.div>
    );
  };

  // Filter options
  const filterOptions: { type: FilterType; label: string; count: number }[] = [
    { type: 'all', label: 'Todos', count: tasks.length },
    { type: 'appointments', label: 'Compromissos', count: tasks.filter(t => t.task_type === 'appointment').length },
    { type: 'tasks', label: 'Tarefas', count: tasks.filter(t => t.task_type === 'task').length },
    { type: 'completed', label: 'Concluídas', count: tasks.filter(t => t.completed_at).length },
    { type: 'pending', label: 'Pendentes', count: tasks.filter(t => !t.completed_at).length }
  ];

  return (
    <div className="relative min-h-screen bg-[#F7F7F7]">
      <Header onAddClick={() => setIsAddModalOpen(true)} />

      <div className="max-w-7xl mx-auto pt-20 px-6 pb-6">
        {/* Search and Filters */}
        <div className="bg-white rounded-2xl p-6 shadow-sm mb-6">
          <div className="flex flex-col lg:flex-row gap-4 mb-6">
            {/* Search */}
            <div className="flex-1 relative">
              <Search size={20} className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Buscar tarefas..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full h-12 pl-12 pr-4 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Category Filter */}
            <select
              value={selectedCategory || ''}
              onChange={(e) => setSelectedCategory(e.target.value ? parseInt(e.target.value) : null)}
              className="h-12 px-4 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Todas as categorias</option>
              {categories?.map(category => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>

            {/* View Mode Toggle */}
            <div className="flex border border-gray-200 rounded-lg overflow-hidden">
              <button
                onClick={() => setViewMode('list')}
                className={`px-4 py-3 flex items-center gap-2 transition-colors ${
                  viewMode === 'list' ? 'bg-blue-500 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'
                }`}
              >
                <List size={16} />
                Lista
              </button>
              <button
                onClick={() => setViewMode('grid')}
                className={`px-4 py-3 flex items-center gap-2 transition-colors ${
                  viewMode === 'grid' ? 'bg-blue-500 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'
                }`}
              >
                <Grid size={16} />
                Grade
              </button>
            </div>
          </div>

          {/* Filter Tabs */}
          <div className="flex flex-wrap gap-2">
            {filterOptions.map(filter => (
              <button
                key={filter.type}
                onClick={() => setSelectedFilter(filter.type)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                  selectedFilter === filter.type
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                {filter.label} ({filter.count})
              </button>
            ))}
          </div>
        </div>
        {/* Date Navigation */}
        <div className="bg-white rounded-2xl p-6 shadow-sm mb-6">
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={() => navigateDay('prev')}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <ChevronLeft size={20} className="text-gray-600" />
            </button>
            
            <div className="text-center">
              <h2 className="text-xl font-semibold text-gray-900">
                {formatDateDisplay(selectedDate)}
              </h2>
              <p className="text-sm text-gray-600">
                {selectedDate.toLocaleDateString('pt-BR', { 
                  weekday: 'long',
                  day: 'numeric',
                  month: 'long'
                })}
              </p>
            </div>
            
            <button
              onClick={() => navigateDay('next')}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <ChevronRight size={20} className="text-gray-600" />
            </button>
          </div>

          {/* Progress Circle */}
          <div className="flex justify-center">
            <ProgressCircle
              percentage={totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0}
              size={120}
              color="#B4EB00"
              bgColor="#E5E7EB"
            />
          </div>
          <p className="text-center text-gray-600 mt-4">
            {totalTasks > 0 
              ? `${completedTasks} de ${totalTasks} tarefas concluídas`
              : 'Nenhuma tarefa para esta data'
            }
          </p>
        </div>

        {/* Tasks Lists */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Pending Tasks */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">
              Tarefas Pendentes ({pendingTasks.length})
            </h3>
            {pendingTasks.length > 0 ? (
              pendingTasks.map(task => (
                <TaskItem key={task.id} task={task} />
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                <CheckCircle size={48} className="mx-auto mb-4 text-gray-300" />
                <p>Nenhuma tarefa pendente para esta data</p>
              </div>
            )}
          </div>

          {/* Completed Tasks */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">
              Tarefas Concluídas ({completedTasksList.length})
            </h3>
            {completedTasksList.length > 0 ? (
              completedTasksList.map(task => (
                <TaskItem key={task.id} task={task} />
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Clock size={48} className="mx-auto mb-4 text-gray-300" />
                <p>Nenhuma tarefa concluída ainda</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Add Task Modal */}
      <AddTaskModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        selectedDate={selectedDate}
      />
    </div>
  );
};

export default TasksPage;
