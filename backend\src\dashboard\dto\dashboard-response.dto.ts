export class TasksSummaryDto {
  completed: number;
  total: number;
}

export class FinancesSummaryDto {
  spent: number;
  budget: number;
  income: number;
  expenses: number;
  savings: number;
}

export class IdeasSummaryDto {
  today: number;
  total: number;
  favorites: number;
}

export class MonthlyProgressDto {
  appointments: {
    completed: number;
    total: number;
  };
  savings: number;
  ideas: number;
  tasksCompleted: number;
  financialGoalProgress: number;
}

export class UserSummaryDto {
  name: string;
  timezone: string;
}

export class DashboardResponseDto {
  user: UserSummaryDto;
  tasks: TasksSummaryDto;
  finances: FinancesSummaryDto;
  ideas: IdeasSummaryDto;
  monthlyProgress: MonthlyProgressDto;
  currentMonth: string;
  currentYear: number;
}
