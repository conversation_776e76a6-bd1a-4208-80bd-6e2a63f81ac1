import { Kysely } from 'kysely';
import { Database } from '../database.types';
export declare class DevService {
    private readonly db;
    constructor(db: Kysely<Database>);
    getOrCreateTestUser(): Promise<{
        password: string | null | undefined;
        timezone: string;
        name: string;
        email: string;
        id: number;
        phone: string | null | undefined;
        created_at: Date;
        updated_at: Date;
        deleted_at: Date | null | undefined;
    }>;
    seedTestData(userId: number): Promise<{
        message: string;
    }>;
}
