import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  Query,
  ParseIntPipe,
  HttpException,
  HttpStatus,
  Logger,
  Put
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { IdeasService } from './ideas.service';
import { CreateIdeaDto } from './dto/create-idea.dto';
import { UpdateIdeaDto } from './dto/update-idea.dto';
import { IdeaResponseDto, IdeaListResponseDto } from './dto/idea-response.dto';
import { CreateIdeaCategoryDto } from './dto/create-idea-category.dto';
import { UpdateIdeaCategoryDto } from './dto/update-idea-category.dto';
import { IdeaCategoryResponseDto } from './dto/idea-category-response.dto';

@Controller('ideas')
@UseGuards(AuthGuard('jwt'))
export class IdeasController {
  private readonly logger = new Logger(IdeasController.name);

  constructor(private readonly ideasService: IdeasService) {}

  @Post()
  async create(@Body() createIdeaDto: CreateIdeaDto, @Request() req): Promise<IdeaResponseDto> {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      return await this.ideasService.create(createIdeaDto, userId);
    } catch (error) {
      this.logger.error('Erro ao criar ideia:', error);
      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get()
  async findAll(
    @Request() req,
    @Query('page') page = 1,
    @Query('limit') limit = 50
  ): Promise<IdeaListResponseDto> {
    try {
      const userId = req.user?.userId;
      const userTimezone = await this.getUserTimezone(userId);

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      return await this.ideasService.findAll(userId, userTimezone, Number(page), Number(limit));
    } catch (error) {
      this.logger.error('Erro ao listar ideias:', error);
      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number, @Request() req): Promise<IdeaResponseDto> {
    try {
      const userId = req.user?.userId;
      const userTimezone = await this.getUserTimezone(userId);

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      return await this.ideasService.findOne(id, userId, userTimezone);
    } catch (error) {
      this.logger.error(`Erro ao buscar ideia ${id}:`, error);

      if (error.message.includes('não encontrada')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }

      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Put(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateIdeaDto: UpdateIdeaDto,
    @Request() req
  ): Promise<IdeaResponseDto> {
    try {
      const userId = req.user?.userId;
      const userTimezone = await this.getUserTimezone(userId);

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      return await this.ideasService.update(id, updateIdeaDto, userId, userTimezone);
    } catch (error) {
      this.logger.error(`Erro ao atualizar ideia ${id}:`, error);

      if (error.message.includes('não encontrada')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }

      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Delete(':id')
  async remove(@Param('id', ParseIntPipe) id: number, @Request() req): Promise<{ message: string }> {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      await this.ideasService.remove(id, userId);
      return { message: 'Ideia removida com sucesso' };
    } catch (error) {
      this.logger.error(`Erro ao remover ideia ${id}:`, error);

      if (error.message.includes('não encontrada')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }

      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Patch(':id/favorite')
  async toggleFavorite(@Param('id', ParseIntPipe) id: number, @Request() req): Promise<IdeaResponseDto> {
    try {
      const userId = req.user?.userId;
      const userTimezone = await this.getUserTimezone(userId);

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      return await this.ideasService.toggleFavorite(id, userId, userTimezone);
    } catch (error) {
      this.logger.error(`Erro ao alterar status de favorito da ideia ${id}:`, error);

      if (error.message.includes('não encontrada')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }

      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  private async getUserTimezone(userId: number): Promise<string> {
    // Implementar busca do timezone do usuário
    // Por enquanto, retornar um padrão
    return 'America/Sao_Paulo';
  }

  // Endpoints para categorias de ideias
  @Post('categories')
  async createCategory(@Body() createCategoryDto: CreateIdeaCategoryDto, @Request() req): Promise<IdeaCategoryResponseDto> {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      return await this.ideasService.createCategory(createCategoryDto, userId);
    } catch (error) {
      this.logger.error('Erro ao criar categoria de ideia:', error);
      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('categories')
  async findAllCategories(@Request() req): Promise<IdeaCategoryResponseDto[]> {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      return await this.ideasService.findAllCategories(userId);
    } catch (error) {
      this.logger.error('Erro ao listar categorias de ideias:', error);
      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('categories/:id')
  async findOneCategory(@Param('id', ParseIntPipe) id: number, @Request() req): Promise<IdeaCategoryResponseDto> {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      return await this.ideasService.findOneCategory(id, userId);
    } catch (error) {
      this.logger.error(`Erro ao buscar categoria de ideia ${id}:`, error);

      if (error.message.includes('não encontrada')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }

      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Put('categories/:id')
  async updateCategory(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateCategoryDto: UpdateIdeaCategoryDto,
    @Request() req
  ): Promise<IdeaCategoryResponseDto> {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      return await this.ideasService.updateCategory(id, updateCategoryDto, userId);
    } catch (error) {
      this.logger.error(`Erro ao atualizar categoria de ideia ${id}:`, error);

      if (error.message.includes('não encontrada')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }

      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Delete('categories/:id')
  async removeCategory(@Param('id', ParseIntPipe) id: number, @Request() req): Promise<{ message: string }> {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      await this.ideasService.removeCategory(id, userId);
      return { message: 'Categoria de ideia removida com sucesso' };
    } catch (error) {
      this.logger.error(`Erro ao remover categoria de ideia ${id}:`, error);

      if (error.message.includes('não encontrada')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }

      if (error.message.includes('está sendo usada')) {
        throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
      }

      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
