import { Controller, Post, Body, UseGuards, Request, Get, HttpException, HttpStatus } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthGuard } from '@nestjs/passport';
import { JwtService } from '@nestjs/jwt';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';

@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly jwtService: JwtService
  ) {}

  @Post('register')
  async register(@Body() registerDto: RegisterDto) {
    return this.authService.register(registerDto);
  }

  @Post('login')
  async login(@Body() loginDto: LoginDto) {
    return this.authService.login(loginDto.email, loginDto.password, loginDto.device_uuid);
  }

  @Post('refresh')
  async refresh(@Body() body: { refresh_token: string; device_uuid: string }) {
    return this.authService.refresh(body.refresh_token, body.device_uuid);
  }

  @Post('logout')
  async logout(@Body() body: { refresh_token: string; device_uuid: string }) {
    await this.authService.logout(body.refresh_token, body.device_uuid);
    return { message: 'Logged out successfully' };
  }

  @UseGuards(AuthGuard('jwt'))
  @Post('protected')
  async protected(@Request() req) {
    return { message: 'This is a protected route', user: req.user };
  }

  @Get('dev-token')
  async generateDevToken() {
    // Só permitir em modo de desenvolvimento
    if (process.env.NODE_ENV !== 'development' || process.env.DEV_MODE_ENABLED !== 'true') {
      throw new HttpException('Endpoint disponível apenas em modo de desenvolvimento', HttpStatus.FORBIDDEN);
    }

    const payload = { sub: 1, email: '<EMAIL>' };
    const access_token = this.jwtService.sign(payload);

    return {
      access_token,
      user: {
        id: 1,
        email: '<EMAIL>',
        name: 'Dev User',
        timezone: 'America/Sao_Paulo'
      },
      message: 'Token de desenvolvimento gerado com sucesso'
    };
  }
}