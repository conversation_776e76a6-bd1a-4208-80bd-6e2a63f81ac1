import { FinancesService } from './finances.service';
import { CreateFinanceDto } from './dto/create-finance.dto';
import { UpdateFinanceDto } from './dto/update-finance.dto';
import { FinanceResponseDto, FinanceListResponseDto, FinanceSummaryDto } from './dto/finance-response.dto';
import { CreateFinanceCategoryDto } from './dto/create-finance-category.dto';
import { UpdateFinanceCategoryDto } from './dto/update-finance-category.dto';
import { FinanceCategoryResponseDto } from './dto/finance-category-response.dto';
export declare class FinancesController {
    private readonly financesService;
    private readonly logger;
    constructor(financesService: FinancesService);
    create(createFinanceDto: CreateFinanceDto, req: any): Promise<FinanceResponseDto>;
    findAll(req: any, page?: number, limit?: number): Promise<FinanceListResponseDto>;
    getSummary(req: any, startDate?: string, endDate?: string): Promise<FinanceSummaryDto>;
    createCategory(createCategoryDto: CreateFinanceCategoryDto, req: any): Promise<FinanceCategoryResponseDto>;
    findAllCategories(req: any): Promise<FinanceCategoryResponseDto[]>;
    findOneCategory(id: number, req: any): Promise<FinanceCategoryResponseDto>;
    updateCategory(id: number, updateCategoryDto: UpdateFinanceCategoryDto, req: any): Promise<FinanceCategoryResponseDto>;
    removeCategory(id: number, req: any): Promise<{
        message: string;
    }>;
    findOne(id: number, req: any): Promise<FinanceResponseDto>;
    update(id: number, updateFinanceDto: UpdateFinanceDto, req: any): Promise<FinanceResponseDto>;
    remove(id: number, req: any): Promise<{
        message: string;
    }>;
    private getUserTimezone;
}
