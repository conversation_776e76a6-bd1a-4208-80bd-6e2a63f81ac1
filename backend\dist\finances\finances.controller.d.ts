import { FinancesService } from './finances.service';
import { CreateFinanceDto } from './dto/create-finance.dto';
import { UpdateFinanceDto } from './dto/update-finance.dto';
import { FinanceResponseDto, FinanceListResponseDto, FinanceSummaryDto } from './dto/finance-response.dto';
export declare class FinancesController {
    private readonly financesService;
    private readonly logger;
    constructor(financesService: FinancesService);
    create(createFinanceDto: CreateFinanceDto, req: any): Promise<FinanceResponseDto>;
    findAll(req: any, page?: number, limit?: number): Promise<FinanceListResponseDto>;
    getSummary(req: any, startDate?: string, endDate?: string): Promise<FinanceSummaryDto>;
    findOne(id: number, req: any): Promise<FinanceResponseDto>;
    update(id: number, updateFinanceDto: UpdateFinanceDto, req: any): Promise<FinanceResponseDto>;
    remove(id: number, req: any): Promise<{
        message: string;
    }>;
    private getUserTimezone;
}
