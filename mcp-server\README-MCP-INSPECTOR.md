# 🔧 Dupli MCP Server - <PERSON><PERSON><PERSON> para MCP Inspector

## ✅ Status do Servidor
- **Status**: ✅ Funcionando (Erro de headers corrigido)
- **Porta**: 3010
- **Endpoint MCP**: `http://localhost:3010/mcp`
- **Health Check**: `http://localhost:3010/health`
- **Última correção**: Headers SSE - problema de conflito com CORS resolvido

## 🚀 Como Iniciar o Servidor

```bash
# 1. Navegar para a pasta do MCP server
cd mcp-server

# 2. Instalar dependências (se necessário)
npm install

# 3. Compilar o código
npm run build

# 4. Iniciar o servidor
node dist/index.js
```

## 🔍 Configuração para MCP Inspector

### Método 1: URL Direta
No MCP Inspector, use a seguinte configuração:

- **Transport Type**: `SSE (Server-Sent Events)`
- **URL**: `http://localhost:3010/mcp`
- **Method**: `GET`

### Método 2: Arquivo de Configuração
Use o arquivo `mcp-inspector-config.json`:

```json
{
  "name": "Dupli MCP Server",
  "description": "MCP Server para o sistema Dupli com ferramentas de produtividade",
  "transport": {
    "type": "sse",
    "url": "http://localhost:3010/mcp"
  },
  "capabilities": {
    "tools": true,
    "resources": false,
    "prompts": false
  }
}
```

## 🛠️ Ferramentas Disponíveis

O servidor MCP oferece as seguintes ferramentas:

1. **📋 get-tasks** - Listar tarefas do usuário
2. **➕ create-task** - Criar nova tarefa
3. **✏️ update-task** - Atualizar tarefa existente
4. **🗑️ delete-task** - Deletar tarefa
5. **📊 get-dashboard** - Obter dados do dashboard
6. **💰 get-finances** - Listar transações financeiras
7. **💸 create-finance** - Criar nova transação financeira
8. **💡 get-ideas** - Listar ideias do usuário
9. **🆕 create-idea** - Criar nova ideia

## 🧪 Teste de Funcionamento

Execute o script de teste para verificar se tudo está funcionando:

```bash
node test-mcp.js
```

**Saída esperada:**
```
🧪 Testando MCP Server...
✅ Health check: { status: 'ok', timestamp: '...' }
🔗 Testando conexão MCP...
✅ Conexão MCP estabelecida
📊 Headers: {
  'content-type': 'text/event-stream',
  'cache-control': 'no-cache',
  ...
}
📨 Chunk 1: event: endpoint
data: /mcp?sessionId=...
```

## 🔧 Solução de Problemas

### Problema: Timeout no MCP Inspector
**Solução**: 
1. Verificar se o servidor está rodando: `netstat -ano | findstr :3010`
2. Testar health check: `curl http://localhost:3010/health`
3. Verificar logs do servidor

### Problema: Ferramentas não aparecem
**Solução**:
1. Verificar se o endpoint `/mcp` está respondendo
2. Confirmar que os headers SSE estão corretos
3. Verificar se o handshake MCP está funcionando

### Problema: Erro de CORS
**Solução**:
- O servidor já está configurado com CORS permissivo
- Headers incluem: `Access-Control-Allow-Origin: *`

## 📝 Logs e Debug

O servidor produz logs detalhados:
- `🚀 Iniciando MCP Server...`
- `✅ Dupli MCP Server running on http://0.0.0.0:3010`
- `🔗 MCP Endpoint: http://0.0.0.0:3010/mcp`
- `🏥 Health check: http://0.0.0.0:3010/health`

## 🔗 Endpoints Disponíveis

- **GET/POST** `/mcp` - Endpoint principal MCP (SSE)
- **GET** `/health` - Health check
- **OPTIONS** `/mcp` - CORS preflight

## ✨ Recursos Implementados

- ✅ **SSE Transport**: Server-Sent Events funcionando
- ✅ **CORS**: Configurado para permitir todas as origens
- ✅ **Health Check**: Endpoint de verificação de status
- ✅ **Keepalive**: Mantém conexão ativa
- ✅ **Error Handling**: Tratamento de erros robusto
- ✅ **Tools Integration**: Integração com API do Dupli

Agora o MCP Server está pronto para uso com o MCP Inspector! 🎉
