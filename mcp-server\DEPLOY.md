# 🚀 Deploy Guide - MCP Server no VPS

## 📋 Pré-requisitos

- ✅ Node.js 18+ instalado
- ✅ Git instalado
- ✅ PM2 para gerenciar processos (opcional)

## 🛠️ Passos para Deploy

### 1. **Clonar/Copiar código para VPS**

```bash
# Opção 1: Via Git
git clone <seu-repo>
cd mcp-server

# Opção 2: Via SCP (do local para VPS)
scp -r mcp-server/ user@seu-vps:/home/<USER>/
```

### 2. **Instalar dependências**

```bash
cd /home/<USER>/mcp-server
npm install
```

### 3. **Configurar variáveis de ambiente**

```bash
# Criar arquivo .env
cat > .env << EOF
API_BASE_URL=http://localhost:3000
N8N_API_KEY=sua-chave-api-aqui
PORT=3001
EOF
```

### 4. **Build do projeto**

```bash
npm run build
```

### 5. **Testar execução**

```bash
npm start
```

Se tudo estiver funcionando, você verá:
```
Dupli MCP Server running on http://localhost:3001
SSE Endpoint: http://localhost:3001/mcp
```

## 🔧 Configuração com PM2 (Recomendado)

### 1. **Instalar PM2**

```bash
npm install -g pm2
```

### 2. **Criar configuração PM2**

```bash
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'dupli-mcp-server',
    script: 'dist/index.js',
    cwd: '/home/<USER>/mcp-server',
    env: {
      NODE_ENV: 'production',
      API_BASE_URL: 'http://localhost:3000',
      N8N_API_KEY: 'sua-chave-api-aqui',
      PORT: 3001
    },
    instances: 1,
    exec_mode: 'fork',
    watch: false,
    max_memory_restart: '1G',
    log_file: '/var/log/dupli-mcp-server.log',
    out_file: '/var/log/dupli-mcp-server-out.log',
    error_file: '/var/log/dupli-mcp-server-error.log',
    restart_delay: 4000
  }]
}
EOF
```

### 3. **Iniciar com PM2**

```bash
# Iniciar aplicação
pm2 start ecosystem.config.js

# Salvar configuração para reiniciar após reboot
pm2 save
pm2 startup

# Verificar status
pm2 status
pm2 logs dupli-mcp-server
```

## 🌐 Configuração de Firewall

Se estiver usando firewall, abra a porta:

```bash
# UFW
sudo ufw allow 3001

# iptables
sudo iptables -A INPUT -p tcp --dport 3001 -j ACCEPT
```

## 🔒 Configuração com Nginx (Opcional)

Para usar com domínio e SSL:

```bash
# Instalar Nginx
sudo apt update
sudo apt install nginx

# Configurar virtual host
sudo nano /etc/nginx/sites-available/mcp-server

# Adicionar configuração:
server {
    listen 80;
    server_name mcp.seu-dominio.com;

    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}

# Ativar configuração
sudo ln -s /etc/nginx/sites-available/mcp-server /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## 📊 Monitoramento

### 1. **Verificar logs**

```bash
# PM2 logs
pm2 logs dupli-mcp-server

# Logs do sistema
tail -f /var/log/dupli-mcp-server.log
```

### 2. **Verificar status**

```bash
# Status do processo
pm2 status

# Status da aplicação
curl http://localhost:3001/mcp
```

### 3. **Restart se necessário**

```bash
# Restart aplicação
pm2 restart dupli-mcp-server

# Reload (zero downtime)
pm2 reload dupli-mcp-server
```

## 🚨 Troubleshooting

### **Erro: "ERR_REQUIRE_ESM"**
✅ **Já resolvido!** O package.json agora usa `"type": "module"`

### **Erro: "EADDRINUSE"**
```bash
# Verificar qual processo está usando a porta
sudo netstat -tulpn | grep :3001

# Matar processo se necessário
sudo kill -9 <PID>
```

### **Erro: "Cannot connect to API"**
- Verifique se a API do backend está rodando
- Confirme as variáveis de ambiente
- Teste conectividade: `curl http://localhost:3000/agentwpp/health`

### **Erro: "Permission denied"**
```bash
# Dar permissões corretas
sudo chown -R $USER:$USER /home/<USER>/mcp-server
chmod +x /home/<USER>/mcp-server/dist/index.js
```

## 📝 Comandos Úteis

```bash
# Verificar se está rodando
curl http://localhost:3001/mcp

# Verificar logs em tempo real
pm2 logs --lines 50

# Reiniciar aplicação
pm2 restart dupli-mcp-server

# Parar aplicação
pm2 stop dupli-mcp-server

# Remover da lista PM2
pm2 delete dupli-mcp-server

# Verificar recursos
pm2 monit
```

## 🔄 Atualização

Para atualizar o código:

```bash
# Parar aplicação
pm2 stop dupli-mcp-server

# Atualizar código
git pull  # ou copiar novos arquivos

# Reinstalar dependências (se necessário)
npm install

# Rebuild
npm run build

# Iniciar novamente
pm2 start dupli-mcp-server
```

## ✅ Configuração Final no N8N

Com o servidor rodando no VPS, configure no N8N:

- **Tipo**: SSE Endpoint
- **URL**: `http://seu-ip-vps:3009/mcp` ou `http://mcp.seu-dominio.com/mcp`
- **Variáveis de ambiente**:
  ```json
  {
    "API_BASE_URL": "http://localhost:3000",
    "N8N_API_KEY": "sua-chave-api"
  }
  ```

## 🧪 Testando com MCP Inspector

Para testar se o servidor está funcionando corretamente:

1. **Acesse o MCP Inspector**: https://modelcontextprotocol.io/docs/tools/inspector
2. **Configure a URL**: `http://seu-ip-vps:3009/mcp`
3. **Teste a conexão**

### Teste manual via curl:

```bash
# Teste health check
curl http://seu-ip-vps:3009/health

# Teste endpoint MCP
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Accept: text/event-stream" \
  -d '{"jsonrpc":"2.0","id":1,"method":"initialize","params":{"protocolVersion":"2024-11-05","capabilities":{"tools":{}},"clientInfo":{"name":"Test","version":"1.0.0"}}}' \
  http://seu-ip-vps:3009/mcp
```

## 🔍 Diagnóstico de Problemas

### Erro "Cannot POST /mcp"
✅ **Resolvido!** O servidor agora suporta GET e POST em `/mcp`

### Erro "Connection failed"
1. Verifique se o servidor está rodando: `pm2 status`
2. Teste health check: `curl http://localhost:3009/health`
3. Verifique logs: `pm2 logs dupli-mcp-server`
4. Verifique firewall: `sudo ufw status`

### Erro "Headers already sent"
✅ **Resolvido!** Corrigido o problema de headers SSE

🎉 **Pronto! Seu MCP Server está rodando em produção!**
