import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { authenticatedApi } from '../lib/api';
import {
  FinanceListResponseDto,
  FinanceResponseDto,
  CreateFinanceDto,
  UpdateFinanceDto,
  FinanceSummaryDto,
  FinanceCategoryResponseDto,
  CreateFinanceCategoryDto,
  UpdateFinanceCategoryDto,
  PaginationParams,
} from '../types/api';

// Hook para listar finances
export const useFinances = (params?: PaginationParams) => {
  return useQuery({
    queryKey: ['finances', params],
    queryFn: async (): Promise<FinanceListResponseDto> => {
      const searchParams = new URLSearchParams();
      if (params?.page) searchParams.append('page', params.page.toString());
      if (params?.limit) searchParams.append('limit', params.limit.toString());
      
      const response = await authenticatedApi.get(`finances?${searchParams.toString()}`);
      return response.json();
    },
    staleTime: 2 * 60 * 1000, // 2 minutos
  });
};

// Hook para buscar uma finance específica
export const useFinance = (id: number) => {
  return useQuery({
    queryKey: ['finances', id],
    queryFn: async (): Promise<FinanceResponseDto> => {
      const response = await authenticatedApi.get(`finances/${id}`);
      return response.json();
    },
    enabled: !!id,
  });
};

// Hook para resumo financeiro
export const useFinancesSummary = (startDate?: string, endDate?: string) => {
  return useQuery({
    queryKey: ['finances-summary', startDate, endDate],
    queryFn: async (): Promise<FinanceSummaryDto> => {
      const searchParams = new URLSearchParams();
      if (startDate) searchParams.append('startDate', startDate);
      if (endDate) searchParams.append('endDate', endDate);
      
      const response = await authenticatedApi.get(`finances/summary?${searchParams.toString()}`);
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para criar finance
export const useCreateFinance = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateFinanceDto): Promise<FinanceResponseDto> => {
      const response = await authenticatedApi.post('finances', { json: data });
      return response.json();
    },
    onSuccess: () => {
      // Invalidar cache das finances, summary e dashboard
      queryClient.invalidateQueries({ queryKey: ['finances'] });
      queryClient.invalidateQueries({ queryKey: ['finances-summary'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
    },
  });
};

// Hook para atualizar finance
export const useUpdateFinance = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: number; data: UpdateFinanceDto }): Promise<FinanceResponseDto> => {
      const response = await authenticatedApi.put(`finances/${id}`, { json: data });
      return response.json();
    },
    onSuccess: (data) => {
      // Invalidar cache das finances, summary e dashboard
      queryClient.invalidateQueries({ queryKey: ['finances'] });
      queryClient.invalidateQueries({ queryKey: ['finances-summary'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
      // Atualizar cache da finance específica
      queryClient.setQueryData(['finances', data.id], data);
    },
  });
};

// Hook para deletar finance
export const useDeleteFinance = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number): Promise<void> => {
      await authenticatedApi.delete(`finances/${id}`);
    },
    onSuccess: () => {
      // Invalidar cache das finances, summary e dashboard
      queryClient.invalidateQueries({ queryKey: ['finances'] });
      queryClient.invalidateQueries({ queryKey: ['finances-summary'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
    },
  });
};

// Hook para listar categorias de finances
export const useFinanceCategories = () => {
  return useQuery({
    queryKey: ['finance-categories'],
    queryFn: async (): Promise<FinanceCategoryResponseDto[]> => {
      const response = await authenticatedApi.get('finances/categories');
      return response.json();
    },
    staleTime: 10 * 60 * 1000, // 10 minutos (categorias mudam menos)
  });
};

// Hook para criar categoria de finance
export const useCreateFinanceCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateFinanceCategoryDto): Promise<FinanceCategoryResponseDto> => {
      const response = await authenticatedApi.post('finances/categories', { json: data });
      return response.json();
    },
    onSuccess: () => {
      // Invalidar cache das categorias
      queryClient.invalidateQueries({ queryKey: ['finance-categories'] });
    },
  });
};

// Hook para atualizar categoria de finance
export const useUpdateFinanceCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: number; data: UpdateFinanceCategoryDto }): Promise<FinanceCategoryResponseDto> => {
      const response = await authenticatedApi.put(`finances/categories/${id}`, { json: data });
      return response.json();
    },
    onSuccess: () => {
      // Invalidar cache das categorias
      queryClient.invalidateQueries({ queryKey: ['finance-categories'] });
    },
  });
};

// Hook para deletar categoria de finance
export const useDeleteFinanceCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number): Promise<void> => {
      await authenticatedApi.delete(`finances/categories/${id}`);
    },
    onSuccess: () => {
      // Invalidar cache das categorias
      queryClient.invalidateQueries({ queryKey: ['finance-categories'] });
    },
  });
};
