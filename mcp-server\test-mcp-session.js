#!/usr/bin/env node

// Teste para diagnosticar problema de sessão MCP
const SERVER_URL = 'http://localhost:3001/mcp';

console.log('🔍 Teste de sessão MCP...');

// Primeiro, fazer o POST para obter o endpoint da sessão
const mcpInitialize = {
  "jsonrpc": "2.0",
  "id": 1,
  "method": "initialize",
  "params": {
    "protocolVersion": "2024-11-05",
    "capabilities": {
      "tools": {}
    },
    "clientInfo": {
      "name": "Test Client",
      "version": "1.0.0"
    }
  }
};

console.log('\n📤 Enviando POST para obter endpoint da sessão...');

try {
  const response = await fetch(SERVER_URL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'text/event-stream',
      'Cache-Control': 'no-cache'
    },
    body: JSON.stringify(mcpInitialize)
  });

  console.log('Response Status:', response.status);
  console.log('Content-Type:', response.headers.get('content-type'));
  
  if (!response.ok) {
    console.log('❌ Response not OK');
    const text = await response.text();
    console.log('Response body:', text);
    process.exit(1);
  }

  console.log('\n📊 Lendo resposta SSE...');
  
  // Ler stream para capturar o endpoint da sessão
  const reader = response.body.getReader();
  const decoder = new TextDecoder();
  
  let buffer = '';
  let sessionEndpoint = null;
  
  // Timeout para não ficar preso
  const timeout = setTimeout(() => {
    console.log('⏰ Timeout - não recebeu endpoint da sessão');
    process.exit(1);
  }, 5000);

  while (true) {
    const { done, value } = await reader.read();
    
    if (done) break;
    
    buffer += decoder.decode(value, { stream: true });
    
    // Processar linhas SSE
    const lines = buffer.split('\n');
    buffer = lines.pop() || '';
    
    for (const line of lines) {
      if (line.trim()) {
        console.log('📨 SSE:', line);
        
        // Capturar endpoint da sessão
        if (line.startsWith('data: /mcp?sessionId=')) {
          sessionEndpoint = line.replace('data: ', '');
          console.log('✅ Endpoint da sessão capturado:', sessionEndpoint);
          clearTimeout(timeout);
          
          // Agora conectar ao endpoint da sessão
          await testSessionEndpoint(sessionEndpoint);
          process.exit(0);
        }
      }
    }
  }
  
  clearTimeout(timeout);
  
} catch (error) {
  console.error('❌ Error:', error.message);
  process.exit(1);
}

async function testSessionEndpoint(sessionEndpoint) {
  console.log('\n🔗 Conectando ao endpoint da sessão...');
  
  const sessionUrl = `http://localhost:3001${sessionEndpoint}`;
  console.log('URL da sessão:', sessionUrl);
  
  try {
    const response = await fetch(sessionUrl, {
      method: 'GET',
      headers: {
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache'
      }
    });

    console.log('Status da sessão:', response.status);
    console.log('Content-Type:', response.headers.get('content-type'));
    
    if (!response.ok) {
      console.log('❌ Falha na conexão da sessão');
      const text = await response.text();
      console.log('Response body:', text);
      return;
    }

    console.log('✅ Conectado ao endpoint da sessão com sucesso!');
    
    // Agora enviar uma mensagem MCP
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    
    let buffer = '';
    let messageCount = 0;
    
    const timeout = setTimeout(() => {
      console.log('⏰ Timeout na sessão');
    }, 5000);

    while (true) {
      const { done, value } = await reader.read();
      
      if (done) break;
      
      buffer += decoder.decode(value, { stream: true });
      
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      for (const line of lines) {
        if (line.trim()) {
          messageCount++;
          console.log(`📨 Sessão Message ${messageCount}:`, line);
          
          if (messageCount >= 3) {
            console.log('✅ Sessão funcionando!');
            clearTimeout(timeout);
            return;
          }
        }
      }
    }
    
    clearTimeout(timeout);
    
  } catch (error) {
    console.error('❌ Erro na sessão:', error.message);
  }
}
