"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var IdeasService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.IdeasService = void 0;
const common_1 = require("@nestjs/common");
const database_types_1 = require("../database.types");
const timezone_1 = require("../utils/timezone");
let IdeasService = IdeasService_1 = class IdeasService {
    logger = new common_1.Logger(IdeasService_1.name);
    db = database_types_1.db;
    async create(createIdeaDto, userId) {
        try {
            const ideaData = {
                ...createIdeaDto,
                user_id: userId,
                created_at: new Date(),
                updated_at: new Date()
            };
            const result = await this.db
                .insertInto('ideas')
                .values(ideaData)
                .executeTakeFirst();
            if (!result.insertId) {
                throw new Error('Falha ao criar ideia');
            }
            this.logger.log(`Ideia criada com ID ${result.insertId} para usuário ${userId}`);
            return this.findOne(Number(result.insertId), userId);
        }
        catch (error) {
            this.logger.error(`Erro ao criar ideia para usuário ${userId}:`, error);
            throw new Error(`Erro ao criar ideia: ${error.message}`);
        }
    }
    async findAll(userId, userTimezone, page = 1, limit = 50) {
        try {
            const offset = (page - 1) * limit;
            const ideas = await this.db
                .selectFrom('ideas')
                .leftJoin('ideas_categories', 'ideas.category_id', 'ideas_categories.id')
                .select([
                'ideas.id',
                'ideas.category_id',
                'ideas_categories.name as category_name',
                'ideas.name',
                'ideas.description',
                'ideas.content',
                'ideas.is_favorite',
                'ideas.user_id',
                'ideas.created_at',
                'ideas.updated_at'
            ])
                .where('ideas.user_id', '=', userId)
                .orderBy('ideas.created_at', 'desc')
                .limit(limit)
                .offset(offset)
                .execute();
            const totalResult = await this.db
                .selectFrom('ideas')
                .select(this.db.fn.count('id').as('count'))
                .where('user_id', '=', userId)
                .executeTakeFirst();
            const total = Number(totalResult?.count || 0);
            const ideasWithTimezone = ideas.map(idea => ({
                id: idea.id,
                category_id: idea.category_id || undefined,
                category_name: idea.category_name || undefined,
                name: idea.name,
                description: idea.description || undefined,
                content: idea.content || undefined,
                is_favorite: idea.is_favorite || undefined,
                user_id: idea.user_id,
                created_at: (0, timezone_1.toUserTimezone)(idea.created_at, userTimezone),
                updated_at: (0, timezone_1.toUserTimezone)(idea.updated_at, userTimezone)
            }));
            this.logger.debug(`Listadas ${ideas.length} ideias para usuário ${userId}`);
            return {
                ideas: ideasWithTimezone,
                total,
                page,
                limit
            };
        }
        catch (error) {
            this.logger.error(`Erro ao listar ideias para usuário ${userId}:`, error);
            throw new Error(`Erro ao listar ideias: ${error.message}`);
        }
    }
    async findOne(id, userId, userTimezone = 'America/Sao_Paulo') {
        try {
            const idea = await this.db
                .selectFrom('ideas')
                .leftJoin('ideas_categories', 'ideas.category_id', 'ideas_categories.id')
                .select([
                'ideas.id',
                'ideas.category_id',
                'ideas_categories.name as category_name',
                'ideas.name',
                'ideas.description',
                'ideas.content',
                'ideas.is_favorite',
                'ideas.user_id',
                'ideas.created_at',
                'ideas.updated_at'
            ])
                .where('ideas.id', '=', id)
                .where('ideas.user_id', '=', userId)
                .executeTakeFirst();
            if (!idea) {
                throw new common_1.NotFoundException(`Ideia com ID ${id} não encontrada`);
            }
            return {
                id: idea.id,
                category_id: idea.category_id || undefined,
                category_name: idea.category_name || undefined,
                name: idea.name,
                description: idea.description || undefined,
                content: idea.content || undefined,
                is_favorite: idea.is_favorite || undefined,
                user_id: idea.user_id,
                created_at: (0, timezone_1.toUserTimezone)(idea.created_at, userTimezone),
                updated_at: (0, timezone_1.toUserTimezone)(idea.updated_at, userTimezone)
            };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            this.logger.error(`Erro ao buscar ideia ${id} para usuário ${userId}:`, error);
            throw new Error(`Erro ao buscar ideia: ${error.message}`);
        }
    }
    async update(id, updateIdeaDto, userId, userTimezone = 'America/Sao_Paulo') {
        try {
            await this.findOne(id, userId, userTimezone);
            const updateData = {
                ...updateIdeaDto,
                updated_at: new Date()
            };
            Object.keys(updateData).forEach(key => {
                if (updateData[key] === undefined) {
                    delete updateData[key];
                }
            });
            await this.db
                .updateTable('ideas')
                .set(updateData)
                .where('id', '=', id)
                .where('user_id', '=', userId)
                .execute();
            this.logger.log(`Ideia ${id} atualizada para usuário ${userId}`);
            return this.findOne(id, userId, userTimezone);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            this.logger.error(`Erro ao atualizar ideia ${id} para usuário ${userId}:`, error);
            throw new Error(`Erro ao atualizar ideia: ${error.message}`);
        }
    }
    async remove(id, userId) {
        try {
            await this.findOne(id, userId);
            await this.db
                .deleteFrom('ideas')
                .where('id', '=', id)
                .where('user_id', '=', userId)
                .execute();
            this.logger.log(`Ideia ${id} removida para usuário ${userId}`);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            this.logger.error(`Erro ao remover ideia ${id} para usuário ${userId}:`, error);
            throw new Error(`Erro ao remover ideia: ${error.message}`);
        }
    }
    async toggleFavorite(id, userId, userTimezone = 'America/Sao_Paulo') {
        try {
            const idea = await this.findOne(id, userId, userTimezone);
            const newFavoriteStatus = !idea.is_favorite;
            await this.db
                .updateTable('ideas')
                .set({
                is_favorite: newFavoriteStatus,
                updated_at: new Date()
            })
                .where('id', '=', id)
                .where('user_id', '=', userId)
                .execute();
            this.logger.log(`Ideia ${id} ${newFavoriteStatus ? 'marcada como favorita' : 'desmarcada como favorita'} para usuário ${userId}`);
            return this.findOne(id, userId, userTimezone);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            this.logger.error(`Erro ao alterar status de favorito da ideia ${id} para usuário ${userId}:`, error);
            throw new Error(`Erro ao alterar status de favorito: ${error.message}`);
        }
    }
    async createCategory(createCategoryDto, userId) {
        try {
            const categoryData = {
                ...createCategoryDto,
                user_id: userId,
                created_at: new Date(),
                updated_at: new Date()
            };
            const result = await this.db
                .insertInto('ideas_categories')
                .values(categoryData)
                .executeTakeFirst();
            if (!result.insertId) {
                throw new Error('Falha ao criar categoria de ideia');
            }
            this.logger.log(`Categoria de ideia criada com ID ${result.insertId} para usuário ${userId}`);
            return this.findOneCategory(Number(result.insertId), userId);
        }
        catch (error) {
            this.logger.error(`Erro ao criar categoria de ideia para usuário ${userId}:`, error);
            throw new Error(`Erro ao criar categoria de ideia: ${error.message}`);
        }
    }
    async findAllCategories(userId) {
        try {
            const categories = await this.db
                .selectFrom('ideas_categories')
                .selectAll()
                .where('user_id', '=', userId)
                .orderBy('name', 'asc')
                .execute();
            this.logger.debug(`Listadas ${categories.length} categorias de ideias para usuário ${userId}`);
            return categories;
        }
        catch (error) {
            this.logger.error(`Erro ao listar categorias de ideias para usuário ${userId}:`, error);
            throw new Error(`Erro ao listar categorias de ideias: ${error.message}`);
        }
    }
    async findOneCategory(id, userId) {
        try {
            const category = await this.db
                .selectFrom('ideas_categories')
                .selectAll()
                .where('id', '=', id)
                .where('user_id', '=', userId)
                .executeTakeFirst();
            if (!category) {
                throw new common_1.NotFoundException(`Categoria de ideia com ID ${id} não encontrada`);
            }
            return category;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            this.logger.error(`Erro ao buscar categoria de ideia ${id} para usuário ${userId}:`, error);
            throw new Error(`Erro ao buscar categoria de ideia: ${error.message}`);
        }
    }
    async updateCategory(id, updateCategoryDto, userId) {
        try {
            await this.findOneCategory(id, userId);
            const updateData = {
                ...updateCategoryDto,
                updated_at: new Date()
            };
            await this.db
                .updateTable('ideas_categories')
                .set(updateData)
                .where('id', '=', id)
                .where('user_id', '=', userId)
                .execute();
            this.logger.log(`Categoria de ideia ${id} atualizada para usuário ${userId}`);
            return this.findOneCategory(id, userId);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            this.logger.error(`Erro ao atualizar categoria de ideia ${id} para usuário ${userId}:`, error);
            throw new Error(`Erro ao atualizar categoria de ideia: ${error.message}`);
        }
    }
    async removeCategory(id, userId) {
        try {
            await this.findOneCategory(id, userId);
            const ideasUsingCategory = await this.db
                .selectFrom('ideas')
                .select(['id'])
                .where('category_id', '=', id)
                .where('user_id', '=', userId)
                .execute();
            if (ideasUsingCategory.length > 0) {
                throw new Error('Não é possível remover categoria que está sendo usada por ideias');
            }
            await this.db
                .deleteFrom('ideas_categories')
                .where('id', '=', id)
                .where('user_id', '=', userId)
                .execute();
            this.logger.log(`Categoria de ideia ${id} removida para usuário ${userId}`);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            this.logger.error(`Erro ao remover categoria de ideia ${id} para usuário ${userId}:`, error);
            throw new Error(`Erro ao remover categoria de ideia: ${error.message}`);
        }
    }
};
exports.IdeasService = IdeasService;
exports.IdeasService = IdeasService = IdeasService_1 = __decorate([
    (0, common_1.Injectable)()
], IdeasService);
//# sourceMappingURL=ideas.service.js.map