#!/usr/bin/env node

// Teste final para MCP Inspector
const SERVER_URL = 'http://localhost:3009/mcp';

console.log('🎯 Teste final para MCP Inspector');
console.log('URL:', SERVER_URL);

async function testMCPInspector() {
  console.log('\n📡 Testando fluxo completo do MCP Inspector...');
  
  try {
    // Teste de saúde primeiro
    console.log('🏥 Verificando saúde do servidor...');
    
    const healthResponse = await fetch('http://localhost:3009/health');
    if (healthResponse.ok) {
      const health = await healthResponse.json();
      console.log('✅ Servidor saudável:', health);
    } else {
      console.log('❌ Servidor não está respondendo');
      return;
    }
    
    // Teste 1: Conexão inicial GET
    console.log('\n1️⃣ Estabelecendo conexão SSE via GET...');
    
    const getResponse = await fetch(SERVER_URL, {
      method: 'GET',
      headers: {
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive'
      }
    });

    console.log('GET Status:', getResponse.status);
    console.log('GET Headers:', Object.fromEntries(getResponse.headers.entries()));
    
    if (!getResponse.ok) {
      console.log('❌ Falha na conexão GET');
      return;
    }
    
    console.log('✅ Conexão GET estabelecida');
    
    // Teste 2: Enviar initialize
    console.log('\n2️⃣ Enviando initialize...');
    
    const initMessage = {
      jsonrpc: '2.0',
      id: 1,
      method: 'initialize',
      params: {
        protocolVersion: '2024-11-05',
        capabilities: {
          tools: {}
        },
        clientInfo: {
          name: 'MCP Inspector',
          version: '1.0.0'
        }
      }
    };

    const initResponse = await fetch(SERVER_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache'
      },
      body: JSON.stringify(initMessage)
    });

    console.log('Initialize Status:', initResponse.status);
    
    if (!initResponse.ok) {
      console.log('❌ Initialize falhou');
      return;
    }
    
    console.log('✅ Initialize enviado');
    
    // Ler resposta
    const reader = initResponse.body.getReader();
    const decoder = new TextDecoder();
    
    let buffer = '';
    let initReceived = false;
    
    const timeout = setTimeout(() => {
      if (!initReceived) {
        console.log('❌ Timeout no initialize');
        process.exit(1);
      }
    }, 5000);

    while (true) {
      const { done, value } = await reader.read();
      
      if (done) break;
      
      buffer += decoder.decode(value, { stream: true });
      
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      for (const line of lines) {
        if (line.trim() && line.startsWith('data: ')) {
          const data = line.replace('data: ', '');
          
          try {
            const parsed = JSON.parse(data);
            console.log('📨 Initialize response:', parsed);
            
            if (parsed.jsonrpc === '2.0' && parsed.id === 1 && parsed.result) {
              console.log('✅ Initialize OK!');
              initReceived = true;
              clearTimeout(timeout);
              
              // Teste 3: tools/list
              await testToolsList();
              return;
            }
          } catch (e) {
            console.log('📋 Non-JSON:', data);
          }
        }
      }
    }
    
    clearTimeout(timeout);
    
  } catch (error) {
    console.error('❌ Erro:', error.message);
    console.error('Stack:', error.stack);
  }
}

async function testToolsList() {
  console.log('\n3️⃣ Testando tools/list...');
  
  const toolsMessage = {
    jsonrpc: '2.0',
    id: 2,
    method: 'tools/list',
    params: {}
  };

  try {
    const response = await fetch(SERVER_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache'
      },
      body: JSON.stringify(toolsMessage)
    });

    console.log('Tools Status:', response.status);
    
    if (!response.ok) {
      console.log('❌ Tools falhou');
      return;
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    
    let buffer = '';
    let toolsReceived = false;
    
    const timeout = setTimeout(() => {
      if (!toolsReceived) {
        console.log('❌ Timeout no tools/list');
      }
    }, 5000);

    while (true) {
      const { done, value } = await reader.read();
      
      if (done) break;
      
      buffer += decoder.decode(value, { stream: true });
      
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      for (const line of lines) {
        if (line.trim() && line.startsWith('data: ')) {
          const data = line.replace('data: ', '');
          
          try {
            const parsed = JSON.parse(data);
            console.log('📨 Tools response:', parsed);
            
            if (parsed.jsonrpc === '2.0' && parsed.id === 2 && parsed.result) {
              console.log('✅ Tools list OK!');
              
              if (parsed.result.tools) {
                console.log(`📋 Tools encontradas: ${parsed.result.tools.length}`);
                
                parsed.result.tools.forEach((tool, i) => {
                  console.log(`  ${i+1}. ${tool.name} - ${tool.description}`);
                });
                
                toolsReceived = true;
                clearTimeout(timeout);
                
                console.log('\n🎉 SUCESSO! MCP Inspector deve funcionar agora!');
                console.log('🔗 Use este endpoint: http://localhost:3009/mcp');
                
                process.exit(0);
              }
            }
          } catch (e) {
            console.log('📋 Non-JSON:', data);
          }
        }
      }
    }
    
    clearTimeout(timeout);
    
  } catch (error) {
    console.error('❌ Erro no tools/list:', error.message);
  }
}

// Executar teste
testMCPInspector();
