import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { db } from '../database.types';
import { CreateFinanceDto } from './dto/create-finance.dto';
import { UpdateFinanceDto } from './dto/update-finance.dto';
import { FinanceResponseDto, FinanceListResponseDto, FinanceSummaryDto } from './dto/finance-response.dto';
import { fromUserTimezone, toUserTimezone } from '../utils/timezone';

@Injectable()
export class FinancesService {
  private readonly logger = new Logger(FinancesService.name);
  private db = db;

  async create(createFinanceDto: CreateFinanceDto, userId: number, userTimezone: string): Promise<FinanceResponseDto> {
    try {
      const financeData = {
        ...createFinanceDto,
        user_id: userId,
        transaction_date: fromUserTimezone(new Date(createFinanceDto.transaction_date), userTimezone),
        created_at: new Date(),
        updated_at: new Date()
      };

      const result = await this.db
        .insertInto('finances')
        .values(financeData)
        .executeTakeFirst();

      if (!result.insertId) {
        throw new Error('Falha ao criar transação financeira');
      }

      this.logger.log(`Transação financeira criada com ID ${result.insertId} para usuário ${userId}`);
      return this.findOne(Number(result.insertId), userId, userTimezone);
    } catch (error) {
      this.logger.error(`Erro ao criar transação financeira para usuário ${userId}:`, error);
      throw new Error(`Erro ao criar transação financeira: ${error.message}`);
    }
  }

  async findAll(userId: number, userTimezone: string, page = 1, limit = 50): Promise<FinanceListResponseDto> {
    try {
      const offset = (page - 1) * limit;

      // Buscar transações com informações da categoria
      const finances = await this.db
        .selectFrom('finances')
        .leftJoin('finances_categories', 'finances.category_id', 'finances_categories.id')
        .select([
          'finances.id',
          'finances.transaction_type',
          'finances.category_id',
          'finances_categories.name as category_name',
          'finances.is_saving',
          'finances.description',
          'finances.amount',
          'finances.transaction_date',
          'finances.user_id',
          'finances.created_at',
          'finances.updated_at'
        ])
        .where('finances.user_id', '=', userId)
        .orderBy('finances.transaction_date', 'desc')
        .limit(limit)
        .offset(offset)
        .execute();

      // Contar total de transações
      const totalResult = await this.db
        .selectFrom('finances')
        .select(this.db.fn.count('id').as('count'))
        .where('user_id', '=', userId)
        .executeTakeFirst();

      const total = Number(totalResult?.count || 0);

      // Converter datas para timezone do usuário
      const financesWithTimezone = finances.map(finance => ({
        id: finance.id,
        transaction_type: finance.transaction_type,
        category_id: finance.category_id || undefined,
        category_name: finance.category_name || undefined,
        is_saving: finance.is_saving || undefined,
        description: finance.description || undefined,
        amount: finance.amount,
        transaction_date: toUserTimezone(finance.transaction_date, userTimezone),
        user_id: finance.user_id,
        created_at: toUserTimezone(finance.created_at, userTimezone),
        updated_at: toUserTimezone(finance.updated_at, userTimezone)
      }));

      this.logger.debug(`Listadas ${finances.length} transações para usuário ${userId}`);

      return {
        finances: financesWithTimezone,
        total,
        page,
        limit
      };
    } catch (error) {
      this.logger.error(`Erro ao listar transações para usuário ${userId}:`, error);
      throw new Error(`Erro ao listar transações: ${error.message}`);
    }
  }

  async findOne(id: number, userId: number, userTimezone: string): Promise<FinanceResponseDto> {
    try {
      const finance = await this.db
        .selectFrom('finances')
        .leftJoin('finances_categories', 'finances.category_id', 'finances_categories.id')
        .select([
          'finances.id',
          'finances.transaction_type',
          'finances.category_id',
          'finances_categories.name as category_name',
          'finances.is_saving',
          'finances.description',
          'finances.amount',
          'finances.transaction_date',
          'finances.user_id',
          'finances.created_at',
          'finances.updated_at'
        ])
        .where('finances.id', '=', id)
        .where('finances.user_id', '=', userId)
        .executeTakeFirst();

      if (!finance) {
        throw new NotFoundException(`Transação financeira com ID ${id} não encontrada`);
      }

      return {
        id: finance.id,
        transaction_type: finance.transaction_type,
        category_id: finance.category_id || undefined,
        category_name: finance.category_name || undefined,
        is_saving: finance.is_saving || undefined,
        description: finance.description || undefined,
        amount: finance.amount,
        transaction_date: toUserTimezone(finance.transaction_date, userTimezone),
        user_id: finance.user_id,
        created_at: toUserTimezone(finance.created_at, userTimezone),
        updated_at: toUserTimezone(finance.updated_at, userTimezone)
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Erro ao buscar transação ${id} para usuário ${userId}:`, error);
      throw new Error(`Erro ao buscar transação: ${error.message}`);
    }
  }

  async update(id: number, updateFinanceDto: UpdateFinanceDto, userId: number, userTimezone: string): Promise<FinanceResponseDto> {
    try {
      // Verificar se a transação existe e pertence ao usuário
      await this.findOne(id, userId, userTimezone);

      const updateData = {
        ...updateFinanceDto,
        transaction_date: updateFinanceDto.transaction_date ? fromUserTimezone(new Date(updateFinanceDto.transaction_date), userTimezone) : undefined,
        updated_at: new Date()
      };

      // Remover campos undefined
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === undefined) {
          delete updateData[key];
        }
      });

      await this.db
        .updateTable('finances')
        .set(updateData)
        .where('id', '=', id)
        .where('user_id', '=', userId)
        .execute();

      this.logger.log(`Transação ${id} atualizada para usuário ${userId}`);
      return this.findOne(id, userId, userTimezone);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Erro ao atualizar transação ${id} para usuário ${userId}:`, error);
      throw new Error(`Erro ao atualizar transação: ${error.message}`);
    }
  }

  async remove(id: number, userId: number): Promise<void> {
    try {
      // Verificar se a transação existe e pertence ao usuário
      await this.findOne(id, userId, 'UTC'); // Usar UTC para verificação simples

      await this.db
        .deleteFrom('finances')
        .where('id', '=', id)
        .where('user_id', '=', userId)
        .execute();

      this.logger.log(`Transação ${id} removida para usuário ${userId}`);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Erro ao remover transação ${id} para usuário ${userId}:`, error);
      throw new Error(`Erro ao remover transação: ${error.message}`);
    }
  }

  async getSummary(userId: number, userTimezone: string, startDate?: Date, endDate?: Date): Promise<FinanceSummaryDto> {
    try {
      // Se não fornecidas, usar o mês atual
      const now = new Date();
      const start = startDate || new Date(now.getFullYear(), now.getMonth(), 1);
      const end = endDate || new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);

      // Converter para UTC considerando o timezone do usuário
      const startUTC = fromUserTimezone(start, userTimezone);
      const endUTC = fromUserTimezone(end, userTimezone);

      // Buscar transações no período
      const transactions = await this.db
        .selectFrom('finances')
        .select(['transaction_type', 'amount', 'is_saving'])
        .where('user_id', '=', userId)
        .where('transaction_date', '>=', startUTC)
        .where('transaction_date', '<=', endUTC)
        .execute();

      let totalIncome = 0;
      let totalExpenses = 0;
      let totalSavings = 0;

      transactions.forEach(transaction => {
        const amount = parseFloat(transaction.amount);

        if (transaction.transaction_type === 'income') {
          totalIncome += amount;
        } else if (transaction.transaction_type === 'expense') {
          totalExpenses += amount;
          if (transaction.is_saving) {
            totalSavings += amount;
          }
        }
      });

      const balance = totalIncome - totalExpenses;

      this.logger.debug(`Resumo financeiro para usuário ${userId}: receita=${totalIncome}, gastos=${totalExpenses}, economia=${totalSavings}`);

      return {
        totalIncome,
        totalExpenses,
        totalSavings,
        balance,
        period: {
          start: toUserTimezone(startUTC, userTimezone),
          end: toUserTimezone(endUTC, userTimezone)
        }
      };
    } catch (error) {
      this.logger.error(`Erro ao buscar resumo financeiro para usuário ${userId}:`, error);
      throw new Error(`Erro ao buscar resumo financeiro: ${error.message}`);
    }
  }
}
