import React, { useState } from 'react';
import { ArrowLeft, Plus, Edit2, Trash2, X, Check, AlertCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useFinanceCategories, useCreateFinanceCategory, useUpdateFinanceCategory, useDeleteFinanceCategory } from '../hooks/useFinances';
import { CreateFinanceCategoryDto, UpdateFinanceCategoryDto } from '../types/api';

const CategoriesPage: React.FC = () => {
  const navigate = useNavigate();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<any | null>(null);
  const [newCategory, setNewCategory] = useState({
    name: '',
    transaction_type: 'expense' as 'income' | 'expense',
    color: '#B4EB00'
  });

  // Fetch data from API
  const { data: categories, isLoading, error } = useFinanceCategories();
  const createCategoryMutation = useCreateFinanceCategory();
  const updateCategoryMutation = useUpdateFinanceCategory();
  const deleteCategoryMutation = useDeleteFinanceCategory();

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando categorias...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertCircle className="h-8 w-8 text-red-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Erro ao carregar categorias</h2>
          <p className="text-gray-600 mb-4">
            Não foi possível carregar as categorias. Tente novamente.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }

  const handleSave = async () => {
    try {
      if (editingCategory) {
        // Update existing category
        const updateData: UpdateFinanceCategoryDto = {
          name: editingCategory.name,
          transaction_type: editingCategory.transaction_type,
          color: editingCategory.color
        };
        await updateCategoryMutation.mutateAsync({ id: editingCategory.id, data: updateData });
        setEditingCategory(null);
      } else {
        // Create new category
        const categoryData: CreateFinanceCategoryDto = {
          name: newCategory.name,
          transaction_type: newCategory.transaction_type,
          color: newCategory.color
        };
        await createCategoryMutation.mutateAsync(categoryData);
        setNewCategory({ name: '', transaction_type: 'expense', color: '#B4EB00' });
      }
      setIsModalOpen(false);
    } catch (error) {
      console.error('Erro ao salvar categoria:', error);
    }
  };

  const handleDelete = async (id: number) => {
    if (confirm('Tem certeza que deseja excluir esta categoria?')) {
      try {
        await deleteCategoryMutation.mutateAsync(id);
      } catch (error) {
        console.error('Erro ao deletar categoria:', error);
      }
    }
  };

  return (
    <div className="max-w-4xl mx-auto min-h-screen p-6 pb-24 md:pb-6">
      <div className="flex items-center justify-between mb-8">
        <button 
          onClick={() => navigate('/finances')}
          className="flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeft size={20} className="mr-1" />
          <span>Voltar para Visão Geral</span>
        </button>
        
        <button
          onClick={() => {
            setEditingCategory(null);
            setIsModalOpen(true);
          }}
          className="px-6 py-2 border-2 border-black rounded-full flex items-center gap-2 hover:bg-black hover:text-white transition-colors"
        >
          <Plus size={20} />
          <span>Criar Nova Categoria</span>
        </button>
      </div>

      <div className="bg-white rounded-2xl p-6 shadow-sm">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Gerenciar Categorias</h1>
        <p className="text-gray-600 mb-8">
          Organize suas finanças criando categorias personalizadas para classificar suas despesas e receitas.
        </p>

        <div className="space-y-4">
          {categories?.map(category => (
            <motion.div
              key={category.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex items-center justify-between p-4 rounded-xl border border-gray-100 hover:border-gray-200 transition-colors"
            >
              <div className="flex items-center gap-3">
                <div
                  className="w-4 h-4 rounded-full"
                  style={{ backgroundColor: category.color || '#B4EB00' }}
                />
                <div>
                  <span className="font-medium">{category.name}</span>
                  <div className="text-xs text-gray-500 mt-1">
                    {category.transaction_type === 'income' ? 'Receita' : 'Despesa'}
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <button
                  onClick={() => {
                    setEditingCategory(category);
                    setIsModalOpen(true);
                  }}
                  className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                >
                  <Edit2 size={18} />
                </button>
                <button
                  onClick={() => handleDelete(category.id)}
                  className="p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                >
                  <Trash2 size={18} />
                </button>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Modal */}
      <AnimatePresence>
        {isModalOpen && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[100]"
              onClick={() => setIsModalOpen(false)}
            />
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-[calc(100%-2rem)] max-w-md bg-white rounded-2xl p-6 shadow-xl z-[101]"
            >
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-900">
                  {editingCategory ? 'Editar Categoria' : 'Nova Categoria'}
                </h2>
                <button
                  onClick={() => setIsModalOpen(false)}
                  className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <X size={20} className="text-gray-600" />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Nome da Categoria
                  </label>
                  <input
                    type="text"
                    value={editingCategory?.name ?? newCategory.name}
                    onChange={(e) => {
                      if (editingCategory) {
                        setEditingCategory({ ...editingCategory, name: e.target.value });
                      } else {
                        setNewCategory({ ...newCategory, name: e.target.value });
                      }
                    }}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                    placeholder="Ex: Alimentação"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Tipo
                  </label>
                  <select
                    value={editingCategory?.transaction_type ?? newCategory.transaction_type}
                    onChange={(e) => {
                      const value = e.target.value as 'income' | 'expense';
                      if (editingCategory) {
                        setEditingCategory({ ...editingCategory, transaction_type: value });
                      } else {
                        setNewCategory({ ...newCategory, transaction_type: value });
                      }
                    }}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                  >
                    <option value="expense">Despesa</option>
                    <option value="income">Receita</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Cor
                  </label>
                  <input
                    type="color"
                    value={editingCategory?.color ?? newCategory.color}
                    onChange={(e) => {
                      if (editingCategory) {
                        setEditingCategory({ ...editingCategory, color: e.target.value });
                      } else {
                        setNewCategory({ ...newCategory, color: e.target.value });
                      }
                    }}
                    className="w-full h-12 p-1 rounded-lg cursor-pointer"
                  />
                </div>

                <div className="flex gap-4 pt-4">
                  <button
                    onClick={() => setIsModalOpen(false)}
                    className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Cancelar
                  </button>
                  <button
                    onClick={handleSave}
                    disabled={
                      createCategoryMutation.isPending ||
                      updateCategoryMutation.isPending ||
                      !(editingCategory?.name ?? newCategory.name).trim()
                    }
                    className="flex-1 px-4 py-2 bg-black text-white rounded-lg hover:bg-opacity-90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {(createCategoryMutation.isPending || updateCategoryMutation.isPending) ? 'Salvando...' : 'Salvar'}
                  </button>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default CategoriesPage;