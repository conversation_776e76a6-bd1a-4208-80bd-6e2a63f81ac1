import { IdeasService } from './ideas.service';
import { CreateIdeaDto } from './dto/create-idea.dto';
import { UpdateIdeaDto } from './dto/update-idea.dto';
import { IdeaResponseDto, IdeaListResponseDto } from './dto/idea-response.dto';
import { CreateIdeaCategoryDto } from './dto/create-idea-category.dto';
import { UpdateIdeaCategoryDto } from './dto/update-idea-category.dto';
import { IdeaCategoryResponseDto } from './dto/idea-category-response.dto';
export declare class IdeasController {
    private readonly ideasService;
    private readonly logger;
    constructor(ideasService: IdeasService);
    create(createIdeaDto: CreateIdeaDto, req: any): Promise<IdeaResponseDto>;
    findAll(req: any, page?: number, limit?: number): Promise<IdeaListResponseDto>;
    findOne(id: number, req: any): Promise<IdeaResponseDto>;
    update(id: number, updateIdeaDto: UpdateIdeaDto, req: any): Promise<IdeaResponseDto>;
    remove(id: number, req: any): Promise<{
        message: string;
    }>;
    toggleFavorite(id: number, req: any): Promise<IdeaResponseDto>;
    private getUserTimezone;
    createCategory(createCategoryDto: CreateIdeaCategoryDto, req: any): Promise<IdeaCategoryResponseDto>;
    findAllCategories(req: any): Promise<IdeaCategoryResponseDto[]>;
    findOneCategory(id: number, req: any): Promise<IdeaCategoryResponseDto>;
    updateCategory(id: number, updateCategoryDto: UpdateIdeaCategoryDto, req: any): Promise<IdeaCategoryResponseDto>;
    removeCategory(id: number, req: any): Promise<{
        message: string;
    }>;
}
