import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { authenticatedApi } from '../lib/api';
import {
  TaskListResponseDto,
  TaskResponseDto,
  CreateTaskDto,
  UpdateTaskDto,
  TaskCategoryResponseDto,
  CreateTaskCategoryDto,
  UpdateTaskCategoryDto,
  PaginationParams,
} from '../types/api';

// Hook para listar tasks
export const useTasks = (params?: PaginationParams) => {
  return useQuery({
    queryKey: ['tasks', params],
    queryFn: async (): Promise<TaskListResponseDto> => {
      const searchParams = new URLSearchParams();
      if (params?.page) searchParams.append('page', params.page.toString());
      if (params?.limit) searchParams.append('limit', params.limit.toString());
      
      const response = await authenticatedApi.get(`tasks?${searchParams.toString()}`);
      return response.json();
    },
    staleTime: 2 * 60 * 1000, // 2 minutos
  });
};

// Hook para buscar uma task específica
export const useTask = (id: number) => {
  return useQuery({
    queryKey: ['tasks', id],
    queryFn: async (): Promise<TaskResponseDto> => {
      const response = await authenticatedApi.get(`tasks/${id}`);
      return response.json();
    },
    enabled: !!id,
  });
};

// Hook para criar task
export const useCreateTask = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateTaskDto): Promise<TaskResponseDto> => {
      const response = await authenticatedApi.post('tasks', { json: data });
      return response.json();
    },
    onSuccess: () => {
      // Invalidar cache das tasks e dashboard
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
    },
  });
};

// Hook para atualizar task
export const useUpdateTask = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: number; data: UpdateTaskDto }): Promise<TaskResponseDto> => {
      const response = await authenticatedApi.put(`tasks/${id}`, { json: data });
      return response.json();
    },
    onSuccess: (data) => {
      // Invalidar cache das tasks e dashboard
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
      // Atualizar cache da task específica
      queryClient.setQueryData(['tasks', data.id], data);
    },
  });
};

// Hook para deletar task
export const useDeleteTask = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number): Promise<void> => {
      await authenticatedApi.delete(`tasks/${id}`);
    },
    onSuccess: () => {
      // Invalidar cache das tasks e dashboard
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
    },
  });
};

// Hook para marcar task como completa
export const useCompleteTask = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number): Promise<TaskResponseDto> => {
      const response = await authenticatedApi.patch(`tasks/${id}/complete`);
      return response.json();
    },
    onSuccess: (data) => {
      // Invalidar cache das tasks e dashboard
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
      // Atualizar cache da task específica
      queryClient.setQueryData(['tasks', data.id], data);
    },
  });
};

// Hook para listar categorias de tasks
export const useTaskCategories = () => {
  return useQuery({
    queryKey: ['task-categories'],
    queryFn: async (): Promise<TaskCategoryResponseDto[]> => {
      const response = await authenticatedApi.get('tasks/categories');
      return response.json();
    },
    staleTime: 10 * 60 * 1000, // 10 minutos (categorias mudam menos)
  });
};

// Hook para criar categoria de task
export const useCreateTaskCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateTaskCategoryDto): Promise<TaskCategoryResponseDto> => {
      const response = await authenticatedApi.post('tasks/categories', { json: data });
      return response.json();
    },
    onSuccess: () => {
      // Invalidar cache das categorias
      queryClient.invalidateQueries({ queryKey: ['task-categories'] });
    },
  });
};

// Hook para atualizar categoria de task
export const useUpdateTaskCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: number; data: UpdateTaskCategoryDto }): Promise<TaskCategoryResponseDto> => {
      const response = await authenticatedApi.put(`tasks/categories/${id}`, { json: data });
      return response.json();
    },
    onSuccess: () => {
      // Invalidar cache das categorias
      queryClient.invalidateQueries({ queryKey: ['task-categories'] });
    },
  });
};

// Hook para deletar categoria de task
export const useDeleteTaskCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number): Promise<void> => {
      await authenticatedApi.delete(`tasks/categories/${id}`);
    },
    onSuccess: () => {
      // Invalidar cache das categorias
      queryClient.invalidateQueries({ queryKey: ['task-categories'] });
    },
  });
};
