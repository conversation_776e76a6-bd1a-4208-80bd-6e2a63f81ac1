import { CreateFinanceDto } from './dto/create-finance.dto';
import { UpdateFinanceDto } from './dto/update-finance.dto';
import { FinanceResponseDto, FinanceListResponseDto, FinanceSummaryDto } from './dto/finance-response.dto';
export declare class FinancesService {
    private readonly logger;
    private db;
    create(createFinanceDto: CreateFinanceDto, userId: number, userTimezone: string): Promise<FinanceResponseDto>;
    findAll(userId: number, userTimezone: string, page?: number, limit?: number): Promise<FinanceListResponseDto>;
    findOne(id: number, userId: number, userTimezone: string): Promise<FinanceResponseDto>;
    update(id: number, updateFinanceDto: UpdateFinanceDto, userId: number, userTimezone: string): Promise<FinanceResponseDto>;
    remove(id: number, userId: number): Promise<void>;
    getSummary(userId: number, userTimezone: string, startDate?: Date, endDate?: Date): Promise<FinanceSummaryDto>;
}
