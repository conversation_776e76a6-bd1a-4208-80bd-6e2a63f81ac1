import { CreateFinanceDto } from './dto/create-finance.dto';
import { UpdateFinanceDto } from './dto/update-finance.dto';
import { FinanceResponseDto, FinanceListResponseDto, FinanceSummaryDto } from './dto/finance-response.dto';
import { CreateFinanceCategoryDto } from './dto/create-finance-category.dto';
import { UpdateFinanceCategoryDto } from './dto/update-finance-category.dto';
import { FinanceCategoryResponseDto } from './dto/finance-category-response.dto';
export declare class FinancesService {
    private readonly logger;
    private db;
    create(createFinanceDto: CreateFinanceDto, userId: number, userTimezone: string): Promise<FinanceResponseDto>;
    findAll(userId: number, userTimezone: string, page?: number, limit?: number): Promise<FinanceListResponseDto>;
    findOne(id: number, userId: number, userTimezone: string): Promise<FinanceResponseDto>;
    update(id: number, updateFinanceDto: UpdateFinanceDto, userId: number, userTimezone: string): Promise<FinanceResponseDto>;
    remove(id: number, userId: number): Promise<void>;
    getSummary(userId: number, userTimezone: string, startDate?: Date, endDate?: Date): Promise<FinanceSummaryDto>;
    createCategory(createCategoryDto: CreateFinanceCategoryDto, userId: number): Promise<FinanceCategoryResponseDto>;
    findAllCategories(userId: number): Promise<FinanceCategoryResponseDto[]>;
    findOneCategory(id: number, userId: number): Promise<FinanceCategoryResponseDto>;
    updateCategory(id: number, updateCategoryDto: UpdateFinanceCategoryDto, userId: number): Promise<FinanceCategoryResponseDto>;
    removeCategory(id: number, userId: number): Promise<void>;
}
