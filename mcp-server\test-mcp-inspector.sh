#!/bin/bash

# Script para testar conectividade com MCP Inspector
SERVER_URL="http://*************:3009"

echo "🔍 Testando conectividade MCP Inspector..."
echo "URL: $SERVER_URL"

echo
echo "1. Testando health check..."
curl -s -w "\nStatus: %{http_code}\n" "$SERVER_URL/health" || echo "❌ Health check falhou"

echo
echo "2. Testando GET /mcp..."
curl -s -w "\nStatus: %{http_code}\n" \
  -H "Accept: text/event-stream" \
  -H "Cache-Control: no-cache" \
  "$SERVER_URL/mcp" || echo "❌ GET /mcp falhou"

echo
echo "3. Testando POST /mcp..."
curl -s -w "\nStatus: %{http_code}\n" \
  -X POST \
  -H "Content-Type: application/json" \
  -H "Accept: text/event-stream" \
  -d '{"jsonrpc":"2.0","id":1,"method":"initialize","params":{"protocolVersion":"2024-11-05","capabilities":{"tools":{}},"clientInfo":{"name":"MCP Inspector","version":"1.0.0"}}}' \
  "$SERVER_URL/mcp" || echo "❌ POST /mcp falhou"

echo
echo "4. Testando OPTIONS /mcp (CORS)..."
curl -s -w "\nStatus: %{http_code}\n" \
  -X OPTIONS \
  -H "Origin: http://localhost:3000" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: Content-Type" \
  "$SERVER_URL/mcp" || echo "❌ OPTIONS /mcp falhou"

echo
echo "✅ Testes concluídos!"
echo "💡 Use no MCP Inspector: $SERVER_URL/mcp"
