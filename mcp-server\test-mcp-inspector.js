#!/usr/bin/env node

// Teste para simular o comportamento do MCP Inspector
const SERVER_URL = 'http://localhost:3001/mcp';

console.log('🔍 Simulando MCP Inspector...');

async function testMCPInspectorFlow() {
  console.log('\n📡 Conectando ao endpoint MCP...');
  
  try {
    // Primeira conexão - GET para estabelecer SSE
    const response = await fetch(SERVER_URL, {
      method: 'GET',
      headers: {
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache'
      }
    });

    console.log('Status:', response.status);
    console.log('Headers:', Object.fromEntries(response.headers.entries()));
    
    if (!response.ok) {
      console.log('❌ Falha na conexão inicial');
      return;
    }

    console.log('✅ Conexão SSE estabelecida');

    // Ler mensagens iniciais
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    
    let buffer = '';
    let connectionEstablished = false;
    
    // Timeout para a primeira fase
    const initialTimeout = setTimeout(() => {
      console.log('⏰ Timeout na conexão inicial');
      if (!connectionEstablished) {
        console.log('❌ Não recebeu mensagens iniciais');
        process.exit(1);
      }
    }, 5000);

    // Ler mensagens iniciais
    while (true) {
      const { done, value } = await reader.read();
      
      if (done) break;
      
      buffer += decoder.decode(value, { stream: true });
      
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      for (const line of lines) {
        if (line.trim()) {
          console.log('📨 Initial message:', line);
          
          if (line.includes('initialized') || line.includes('ping')) {
            connectionEstablished = true;
            clearTimeout(initialTimeout);
            console.log('✅ Conexão estabelecida, enviando initialize...');
            
            // Agora simular o que o MCP Inspector faz
            await sendInitialize();
            return;
          }
        }
      }
    }
    
    clearTimeout(initialTimeout);
    
  } catch (error) {
    console.error('❌ Erro na conexão:', error.message);
    process.exit(1);
  }
}

async function sendInitialize() {
  console.log('\n🔄 Enviando initialize...');
  
  const initializeMessage = {
    jsonrpc: '2.0',
    id: 1,
    method: 'initialize',
    params: {
      protocolVersion: '2024-11-05',
      capabilities: {
        tools: {}
      },
      clientInfo: {
        name: 'MCP Inspector',
        version: '1.0.0'
      }
    }
  };

  try {
    const response = await fetch(SERVER_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache'
      },
      body: JSON.stringify(initializeMessage)
    });

    console.log('Initialize response status:', response.status);
    
    if (!response.ok) {
      console.log('❌ Initialize falhou');
      return;
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    
    let buffer = '';
    let initializeReceived = false;
    
    const timeout = setTimeout(() => {
      if (!initializeReceived) {
        console.log('❌ Timeout aguardando initialize response');
        process.exit(1);
      }
    }, 5000);

    while (true) {
      const { done, value } = await reader.read();
      
      if (done) break;
      
      buffer += decoder.decode(value, { stream: true });
      
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      for (const line of lines) {
        if (line.trim() && line.startsWith('data: ')) {
          const data = line.replace('data: ', '');
          console.log('📨 Initialize response:', data);
          
          try {
            const parsed = JSON.parse(data);
            
            if (parsed.jsonrpc === '2.0' && parsed.id === 1 && parsed.result) {
              console.log('✅ Initialize response received!');
              initializeReceived = true;
              clearTimeout(timeout);
              
              // Agora enviar tools/list
              await sendToolsList();
              return;
            }
          } catch (e) {
            console.log('📋 Non-JSON initialize message:', data);
          }
        }
      }
    }
    
    clearTimeout(timeout);
    
  } catch (error) {
    console.error('❌ Erro no initialize:', error.message);
  }
}

async function sendToolsList() {
  console.log('\n🔧 Enviando tools/list...');
  
  const toolsListMessage = {
    jsonrpc: '2.0',
    id: 2,
    method: 'tools/list',
    params: {}
  };

  try {
    const response = await fetch(SERVER_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache'
      },
      body: JSON.stringify(toolsListMessage)
    });

    console.log('Tools list response status:', response.status);
    
    if (!response.ok) {
      console.log('❌ Tools list falhou');
      return;
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    
    let buffer = '';
    let toolsReceived = false;
    
    const timeout = setTimeout(() => {
      if (!toolsReceived) {
        console.log('❌ Timeout aguardando tools/list response');
        process.exit(1);
      }
    }, 5000);

    while (true) {
      const { done, value } = await reader.read();
      
      if (done) break;
      
      buffer += decoder.decode(value, { stream: true });
      
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      for (const line of lines) {
        if (line.trim() && line.startsWith('data: ')) {
          const data = line.replace('data: ', '');
          console.log('📨 Tools list response:', data);
          
          try {
            const parsed = JSON.parse(data);
            
            if (parsed.jsonrpc === '2.0' && parsed.id === 2 && parsed.result) {
              console.log('✅ Tools list response received!');
              
              if (parsed.result.tools) {
                console.log(`📋 Found ${parsed.result.tools.length} tools:`);
                parsed.result.tools.forEach((tool, index) => {
                  console.log(`  ${index + 1}. ${tool.name} - ${tool.description}`);
                });
                
                toolsReceived = true;
                clearTimeout(timeout);
                
                console.log('\n🎉 MCP Inspector simulation successful!');
                process.exit(0);
              } else {
                console.log('❌ Tools array not found in response');
              }
            }
          } catch (e) {
            console.log('📋 Non-JSON tools message:', data);
          }
        }
      }
    }
    
    clearTimeout(timeout);
    
  } catch (error) {
    console.error('❌ Erro no tools/list:', error.message);
  }
}

// Executar teste
testMCPInspectorFlow();
