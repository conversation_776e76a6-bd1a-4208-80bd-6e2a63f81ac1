#!/usr/bin/env node

// Teste MCP para porta correta
const SERVER_URL = 'http://localhost:3009/mcp';

console.log('🔍 Testando MCP na porta 3009...');

async function testMCPInspectorConnect() {
  console.log('\n📡 Testando conexão MCP...');
  
  try {
    // Teste 1: GET para estabelecer conexão
    console.log('1️⃣ Fazendo GET para estabelecer conexão SSE...');
    
    const response = await fetch(SERVER_URL, {
      method: 'GET',
      headers: {
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache'
      }
    });

    console.log('Response Status:', response.status);
    console.log('Content-Type:', response.headers.get('content-type'));
    
    if (!response.ok) {
      console.log('❌ Falha no GET');
      return;
    }

    console.log('✅ GET funcionou');

    // Teste 2: Enviar initialize via POST
    console.log('\n2️⃣ Enviando initialize via POST...');
    
    const initializeMessage = {
      jsonrpc: '2.0',
      id: 1,
      method: 'initialize',
      params: {
        protocolVersion: '2024-11-05',
        capabilities: {
          tools: {}
        },
        clientInfo: {
          name: 'MCP Inspector',
          version: '1.0.0'
        }
      }
    };

    const initResponse = await fetch(SERVER_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache'
      },
      body: JSON.stringify(initializeMessage)
    });

    console.log('Initialize response status:', initResponse.status);
    
    if (!initResponse.ok) {
      console.log('❌ Initialize falhou');
      return;
    }

    console.log('✅ Initialize enviado');

    // Ler resposta do initialize
    const reader = initResponse.body.getReader();
    const decoder = new TextDecoder();
    
    let buffer = '';
    let initializeReceived = false;
    
    const timeout = setTimeout(() => {
      if (!initializeReceived) {
        console.log('❌ Timeout aguardando initialize response');
      }
    }, 5000);

    while (true) {
      const { done, value } = await reader.read();
      
      if (done) break;
      
      buffer += decoder.decode(value, { stream: true });
      
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      for (const line of lines) {
        if (line.trim() && line.startsWith('data: ')) {
          const data = line.replace('data: ', '');
          console.log('📨 Initialize response:', data);
          
          try {
            const parsed = JSON.parse(data);
            
            if (parsed.jsonrpc === '2.0' && parsed.id === 1 && parsed.result) {
              console.log('✅ Initialize response received!');
              initializeReceived = true;
              clearTimeout(timeout);
              
              // Teste 3: Enviar tools/list
              await sendToolsList();
              return;
            }
          } catch (e) {
            // Ignorar mensagens não-JSON
          }
        }
      }
    }
    
    clearTimeout(timeout);
    
  } catch (error) {
    console.error('❌ Erro:', error.message);
  }
}

async function sendToolsList() {
  console.log('\n3️⃣ Enviando tools/list...');
  
  const toolsListMessage = {
    jsonrpc: '2.0',
    id: 2,
    method: 'tools/list',
    params: {}
  };

  try {
    const response = await fetch(SERVER_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache'
      },
      body: JSON.stringify(toolsListMessage)
    });

    console.log('Tools list response status:', response.status);
    
    if (!response.ok) {
      console.log('❌ Tools list falhou');
      return;
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    
    let buffer = '';
    let toolsReceived = false;
    
    const timeout = setTimeout(() => {
      if (!toolsReceived) {
        console.log('❌ Timeout aguardando tools/list response');
      }
    }, 5000);

    while (true) {
      const { done, value } = await reader.read();
      
      if (done) break;
      
      buffer += decoder.decode(value, { stream: true });
      
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      for (const line of lines) {
        if (line.trim() && line.startsWith('data: ')) {
          const data = line.replace('data: ', '');
          console.log('📨 Tools response:', data);
          
          try {
            const parsed = JSON.parse(data);
            
            if (parsed.jsonrpc === '2.0' && parsed.id === 2 && parsed.result) {
              console.log('✅ Tools list response received!');
              
              if (parsed.result.tools) {
                console.log(`📋 Found ${parsed.result.tools.length} tools:`);
                parsed.result.tools.forEach((tool, index) => {
                  console.log(`  ${index + 1}. ${tool.name} - ${tool.description}`);
                });
                
                toolsReceived = true;
                clearTimeout(timeout);
                
                console.log('\n🎉 MCP funcionando perfeitamente!');
                console.log('\n💡 Para MCP Inspector, use: http://localhost:3009/mcp');
                process.exit(0);
              }
            }
          } catch (e) {
            // Ignorar mensagens não-JSON
          }
        }
      }
    }
    
    clearTimeout(timeout);
    
  } catch (error) {
    console.error('❌ Erro no tools/list:', error.message);
  }
}

// Executar teste
testMCPInspectorConnect();
