import React from 'react';
import { Eye, CheckCircle, DollarSign, Lightbulb, PiggyBank, AlertCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import ProgressBar from '../components/ProgressBar';
import { useDashboardData } from '../hooks/useDashboard';

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const { data, isLoading, error } = useDashboardData();

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando dashboard...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertCircle className="h-8 w-8 text-red-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Erro ao carregar dashboard</h2>
          <p className="text-gray-600 mb-4">
            Não foi possível carregar os dados do dashboard. Tente novamente.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }

  // Se não há dados, não renderizar
  if (!data) {
    return null;
  }

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return '☀️ Bom dia';
    if (hour < 18) return '🌤️ Boa tarde';
    return '🌙 Boa noite';
  };

  const cards = [
    {
      title: 'Compromissos',
      icon: <CheckCircle size={24} className="text-white" />,
      iconBg: 'bg-gray-900',
      content: (
        <>
          <p className="text-gray-600 text-sm">
            {data.tasks.completed} de {data.tasks.total} tarefas feitas
          </p>
          <ProgressBar
            percentage={data.tasks.total > 0 ? (data.tasks.completed / data.tasks.total) * 100 : 0}
            color="#1ED760"
            bgColor="#E5E7EB"
            height={6}
          />
        </>
      ),
      route: '/tasks',
      bg: 'bg-white'
    },
    {
      title: 'Finanças',
      icon: <DollarSign size={24} className="text-gray-900" />,
      iconBg: 'bg-[#B4EB00]',
      content: (
        <>
          <p className="text-gray-900 text-sm">
            {new Intl.NumberFormat('pt-BR', {
              style: 'currency',
              currency: 'BRL'
            }).format(data.finances.spent)} / {new Intl.NumberFormat('pt-BR', {
              style: 'currency',
              currency: 'BRL'
            }).format(data.finances.budget)}
          </p>
          <ProgressBar
            percentage={data.finances.budget > 0 ? (data.finances.spent / data.finances.budget) * 100 : 0}
            color="#212121"
            bgColor="#E5E7EB"
            height={6}
          />
        </>
      ),
      route: '/finances',
      bg: 'bg-[#B4EB00]'
    },
    {
      title: 'Ideias',
      icon: <Lightbulb size={24} className="text-white" />,
      iconBg: 'bg-gray-900',
      content: (
        <>
          <p className="text-white text-sm">{data.ideas.today} ideias salvas hoje</p>
          <ProgressBar
            percentage={data.ideas.total > 0 ? (data.ideas.today / data.ideas.total) * 100 : 0}
            color="#B4EB00"
            bgColor="rgba(255,255,255,0.2)"
            height={6}
          />
        </>
      ),
      route: '/ideas',
      bg: 'bg-[#212121]'
    }
  ];

  return (
    <div className="min-h-screen bg-[#F7F7F7] pb-24 md:pb-6">
      <div className="max-w-7xl mx-auto px-4 md:px-6">
        <div className="pt-8 pb-6">
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
            {getGreeting()}, {data.user.name}!
          </h1>
          <p className="text-gray-600">
            Vamos deixar tudo organizado para hoje?
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
          {cards.map((card, index) => (
            <motion.div
              key={card.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              onClick={() => navigate(card.route)}
              className={`${card.bg} rounded-2xl p-6 shadow-sm cursor-pointer hover:shadow-md transition-all`}
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg ${card.iconBg}`}>
                    {card.icon}
                  </div>
                  <h2 className={`text-lg font-medium ${
                    card.bg === 'bg-[#212121]' ? 'text-white' : 'text-gray-900'
                  }`}>
                    {card.title}
                  </h2>
                </div>
                <div className="flex flex-col items-center">
                  <Eye size={18} className={card.bg === 'bg-[#212121]' ? 'text-white' : 'text-gray-600'} />
                  <span className={`text-xs mt-0.5 ${
                    card.bg === 'bg-[#212121]' ? 'text-white' : 'text-gray-600'
                  }`}>
                    ver
                  </span>
                </div>
              </div>
              {card.content}
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          onClick={() => navigate('/progresso-mensal')}
          className="mt-6 bg-white rounded-2xl p-6 shadow-sm cursor-pointer hover:shadow-md transition-all"
        >
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-medium text-gray-900">
              Seu progresso esse mês
            </h2>
            <Eye size={18} className="text-gray-600" />
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <p className="text-gray-600 text-sm mb-2">
                {data.monthlyProgress.appointments.completed} de {data.monthlyProgress.appointments.total} compromissos concluídos
              </p>
              <ProgressBar
                percentage={data.monthlyProgress.appointments.total > 0 ? (data.monthlyProgress.appointments.completed / data.monthlyProgress.appointments.total) * 100 : 0}
                color="#1ED760"
                bgColor="#E5E7EB"
                height={6}
              />
            </div>

            <div className="flex lg:flex-col justify-between gap-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gray-100 rounded-lg">
                  <PiggyBank size={20} className="text-gray-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Economizados</p>
                  <p className="font-medium text-gray-900">
                    {new Intl.NumberFormat('pt-BR', {
                      style: 'currency',
                      currency: 'BRL'
                    }).format(data.monthlyProgress.savings)}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="p-2 bg-gray-100 rounded-lg">
                  <Lightbulb size={20} className="text-gray-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Ideias salvas</p>
                  <p className="font-medium text-gray-900">
                    {data.monthlyProgress.ideas}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Dashboard;