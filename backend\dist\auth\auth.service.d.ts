import { JwtService } from '@nestjs/jwt';
import { RegisterDto } from './dto/register.dto';
export declare class AuthService {
    private readonly jwtService;
    private db;
    constructor(jwtService: JwtService);
    register(registerDto: RegisterDto): Promise<{
        status: string;
        data: never[];
    }>;
    validateUser(email: string, password: string): Promise<any>;
    login(email: string, password: string, deviceUuid?: string): Promise<{
        access_token: string;
        refresh_token: string;
        device_uuid: string;
    }>;
    refresh(refreshToken: string, deviceUuid: string): Promise<{
        access_token: string;
        refresh_token: string;
        device_uuid: string;
    }>;
    logout(refreshToken: string, deviceUuid: string): Promise<void>;
}
