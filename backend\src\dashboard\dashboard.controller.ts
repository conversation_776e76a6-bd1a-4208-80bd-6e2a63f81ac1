import {
  Controller,
  Get,
  UseGuards,
  Request,
  HttpException,
  HttpStatus,
  Logger
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { DashboardService } from './dashboard.service';
import { DashboardResponseDto } from './dto/dashboard-response.dto';
import {
  UserNotFoundException,
  DashboardDataException,
  InvalidUserTokenException
} from './exceptions/dashboard.exceptions';

@Controller('dashboard')
@UseGuards(AuthGuard('jwt'))
export class DashboardController {
  private readonly logger = new Logger(DashboardController.name);

  constructor(private readonly dashboardService: DashboardService) {}

  @Get()
  async getDashboard(@Request() req): Promise<DashboardResponseDto> {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        this.logger.warn('Tentativa de acesso ao dashboard sem userId válido');
        throw new InvalidUserTokenException();
      }

      this.logger.log(`Solicitação de dados do dashboard para usuário ${userId}`);
      return await this.dashboardService.getDashboardData(userId);
    } catch (error) {
      if (error instanceof UserNotFoundException ||
          error instanceof DashboardDataException ||
          error instanceof InvalidUserTokenException) {
        throw error;
      }

      this.logger.error('Erro inesperado ao buscar dados do dashboard:', error);
      throw new HttpException(
        'Erro interno do servidor ao buscar dados do dashboard',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}