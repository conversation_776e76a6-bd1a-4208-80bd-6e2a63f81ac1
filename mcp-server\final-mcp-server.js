#!/usr/bin/env node

// MCP Server final - compatível com MCP Inspector
import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import axios from 'axios';

dotenv.config();

// Configurações
const CONFIG = {
  API_BASE_URL: process.env.API_BASE_URL || 'http://localhost:3000',
  API_KEY: process.env.N8N_API_KEY || 'your-secret-api-key-here',
  MCP_SERVER: {
    name: 'dupli-agentwpp',
    version: '1.0.0',
  }
};

// Cliente HTTP
const apiClient = axios.create({
  baseURL: CONFIG.API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': CONFIG.API_KEY
  },
  timeout: 10000
});

// Tools disponíveis
const tools = [
  {
    name: 'check_integration',
    description: 'Verificar se o telefone possui integração WhatsApp ativa',
    inputSchema: {
      type: 'object',
      properties: {
        phone: { type: 'string', description: 'Número de telefone do usuário' }
      },
      required: ['phone']
    }
  },
  {
    name: 'get_dashboard',
    description: 'Obter dados do dashboard do usuário',
    inputSchema: {
      type: 'object',
      properties: {
        phone: { type: 'string', description: 'Número de telefone do usuário' }
      },
      required: ['phone']
    }
  },
  {
    name: 'create_task',
    description: 'Criar uma nova tarefa',
    inputSchema: {
      type: 'object',
      properties: {
        phone: { type: 'string', description: 'Número de telefone do usuário' },
        task_type: { type: 'string', enum: ['appointment', 'task'], description: 'Tipo da tarefa' },
        category_id: { type: 'number', description: 'ID da categoria (opcional)' },
        name: { type: 'string', description: 'Nome da tarefa' },
        description: { type: 'string', description: 'Descrição da tarefa (opcional)' },
        task_date: { type: 'string', description: 'Data da tarefa no formato ISO (opcional)' }
      },
      required: ['phone', 'task_type', 'name']
    }
  },
  {
    name: 'list_tasks',
    description: 'Listar tarefas do usuário com opções de filtro e busca',
    inputSchema: {
      type: 'object',
      properties: {
        phone: { type: 'string', description: 'Número de telefone do usuário' },
        page: { type: 'number', description: 'Número da página (padrão: 1)' },
        limit: { type: 'number', description: 'Limite de itens por página (padrão: 10)' },
        search: { type: 'string', description: 'Texto para buscar no nome ou descrição das tarefas' },
        start_date: { type: 'string', description: 'Data de início para filtro (formato ISO)' },
        end_date: { type: 'string', description: 'Data de fim para filtro (formato ISO)' },
        completed: { type: 'boolean', description: 'Filtrar por tarefas concluídas (true) ou pendentes (false)' },
        category_id: { type: 'number', description: 'Filtrar por ID da categoria' }
      },
      required: ['phone']
    }
  },
];

// Handlers para os tools
const handlers = {
  async check_integration(args) {
    const { phone } = args;
    const response = await apiClient.get(`/agentwpp/check-integration/${phone}`);
    return response.data;
  },

  async get_dashboard(args) {
    const { phone } = args;
    const response = await apiClient.get(`/agentwpp/dashboard/${phone}`);
    return response.data;
  },

  async create_task(args) {
    const response = await apiClient.post('/agentwpp/tasks', args);
    return response.data;
  },

  async list_tasks(args) {
    const { phone, page = 1, limit = 10, search, start_date, end_date, completed, category_id } = args;
    
    const params = { page, limit };
    if (search) params.search = search;
    if (start_date) params.start_date = start_date;
    if (end_date) params.end_date = end_date;
    if (completed !== undefined) params.completed = completed;
    if (category_id) params.category_id = category_id;
    
    const response = await apiClient.get(`/agentwpp/tasks/${phone}`, { params });
    return response.data;
  },
};

// Implementação MCP customizada
class SimpleMCPServer {
  constructor() {
    this.connections = new Map();
  }

  async processMessage(message) {
    console.log('Processing MCP message:', message);
    
    if (message.method === 'initialize') {
      return {
        jsonrpc: '2.0',
        id: message.id,
        result: {
          protocolVersion: '2024-11-05',
          capabilities: {
            tools: {}
          },
          serverInfo: {
            name: CONFIG.MCP_SERVER.name,
            version: CONFIG.MCP_SERVER.version
          }
        }
      };
    }

    if (message.method === 'tools/list') {
      return {
        jsonrpc: '2.0',
        id: message.id,
        result: {
          tools: tools
        }
      };
    }

    if (message.method === 'tools/call') {
      const { name, arguments: args } = message.params;
      
      if (!handlers[name]) {
        return {
          jsonrpc: '2.0',
          id: message.id,
          error: {
            code: -32601,
            message: `Unknown tool: ${name}`
          }
        };
      }

      try {
        const result = await handlers[name](args);
        return {
          jsonrpc: '2.0',
          id: message.id,
          result: {
            content: [
              {
                type: 'text',
                text: JSON.stringify(result, null, 2)
              }
            ]
          }
        };
      } catch (error) {
        console.error(`Error in tool ${name}:`, error);
        return {
          jsonrpc: '2.0',
          id: message.id,
          error: {
            code: -32603,
            message: error.message
          }
        };
      }
    }

    return {
      jsonrpc: '2.0',
      id: message.id,
      error: {
        code: -32601,
        message: 'Method not found'
      }
    };
  }

  handleConnection(req, res) {
    const connectionId = Math.random().toString(36).substr(2, 9);
    console.log(`MCP ${req.method} connection ${connectionId} from ${req.ip}`);
    
    // Configurar headers SSE
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type, Cache-Control, Accept, Authorization',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
    });

    this.connections.set(connectionId, res);

    // Keepalive
    const keepAlive = setInterval(() => {
      if (!res.destroyed) {
        res.write(': keepalive\n\n');
      } else {
        clearInterval(keepAlive);
        this.connections.delete(connectionId);
      }
    }, 30000);

    // Para GET, apenas manter conexão
    if (req.method === 'GET') {
      res.on('close', () => {
        clearInterval(keepAlive);
        this.connections.delete(connectionId);
        console.log(`MCP GET connection ${connectionId} closed`);
      });
      return;
    }

    // Para POST, processar dados
    if (req.method === 'POST') {
      let body = '';
      req.on('data', (chunk) => {
        body += chunk.toString();
      });

      req.on('end', async () => {
        try {
          const message = JSON.parse(body);
          const response = await this.processMessage(message);
          res.write(`data: ${JSON.stringify(response)}\n\n`);
        } catch (error) {
          console.error('Error processing message:', error);
          const errorResponse = {
            jsonrpc: '2.0',
            id: 'error',
            error: {
              code: -32603,
              message: 'Internal error'
            }
          };
          res.write(`data: ${JSON.stringify(errorResponse)}\n\n`);
        }
      });

      res.on('close', () => {
        clearInterval(keepAlive);
        this.connections.delete(connectionId);
        console.log(`MCP POST connection ${connectionId} closed`);
      });
    }
  }

  async run() {
    const app = express();
    
    // Configurar CORS
    app.use(cors({
      origin: '*',
      credentials: true,
      methods: ['GET', 'POST', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'Accept', 'Cache-Control']
    }));

    // Não usar express.json() para MCP - processar manualmente
    app.use('/health', express.json());

    const port = process.env.PORT ? parseInt(process.env.PORT) : 3001;

    // Health check
    app.get('/health', (req, res) => {
      res.json({ status: 'ok', timestamp: new Date().toISOString() });
    });

    // MCP endpoint
    app.all('/mcp', (req, res) => {
      this.handleConnection(req, res);
    });

    // OPTIONS para CORS
    app.options('/mcp', (req, res) => {
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept, Cache-Control');
      res.setHeader('Access-Control-Max-Age', '86400');
      res.status(200).end();
    });

    app.listen(port, '0.0.0.0', () => {
      console.log(`✅ Dupli MCP Server running on http://0.0.0.0:${port}`);
      console.log(`🔗 MCP Endpoint: http://0.0.0.0:${port}/mcp`);
      console.log(`❤️  Health check: http://0.0.0.0:${port}/health`);
      console.log(`🔧 Tools available: ${tools.length}`);
    });
  }
}

// Executar servidor
const server = new SimpleMCPServer();
server.run().catch(console.error);
