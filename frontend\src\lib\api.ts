import ky from 'ky';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

// Função para obter o token do localStorage
const getToken = (): string | null => {
  return localStorage.getItem('auth_token');
};

// Função para obter o device UUID
const getDeviceUuid = (): string => {
  let deviceUuid = localStorage.getItem('device_uuid');
  if (!deviceUuid) {
    deviceUuid = crypto.randomUUID();
    localStorage.setItem('device_uuid', deviceUuid);
  }
  return deviceUuid;
};

// Cliente HTTP base
export const api = ky.create({
  prefixUrl: API_URL,
  timeout: 30000,
  retry: {
    limit: 2,
    methods: ['get', 'put', 'head', 'delete', 'options', 'trace'],
    statusCodes: [408, 413, 429, 500, 502, 503, 504],
  },
  hooks: {
    beforeRequest: [
      (request) => {
        // Adicionar device UUID em todas as requisições
        const deviceUuid = getDeviceUuid();
        request.headers.set('X-Device-UUID', deviceUuid);
        
        // Adicionar token JWT se disponível (exceto para rotas de auth)
        const token = getToken();
        const url = request.url;
        const isAuthRoute = url.includes('/auth/login') || url.includes('/auth/register');
        
        if (token && !isAuthRoute) {
          request.headers.set('Authorization', `Bearer ${token}`);
        }
      },
    ],
    afterResponse: [
      async (request, options, response) => {
        // Se receber 401, limpar token e redirecionar para login
        if (response.status === 401) {
          localStorage.removeItem('auth_token');
          localStorage.removeItem('user_data');
          
          // Só redirecionar se não estivermos já na página de login
          if (!window.location.pathname.includes('/login')) {
            window.location.href = '/login';
          }
        }
        
        return response;
      },
    ],
  },
});

// Cliente para requisições autenticadas
export const authenticatedApi = api.extend({
  hooks: {
    beforeRequest: [
      (request) => {
        const token = getToken();
        if (!token) {
          throw new Error('Token de autenticação não encontrado');
        }
      },
    ],
  },
});

// Tipos para autenticação
export interface LoginRequest {
  email: string;
  password: string;
  device_uuid: string;
}

export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
  phone?: string;
  timezone?: string;
  device_uuid: string;
}

export interface AuthResponse {
  access_token: string;
  refresh_token: string;
  user: {
    id: number;
    name: string;
    email: string;
    phone?: string;
    timezone: string;
  };
}

// Funções de autenticação
export const authApi = {
  login: async (credentials: Omit<LoginRequest, 'device_uuid'>): Promise<AuthResponse> => {
    const deviceUuid = getDeviceUuid();
    const response = await api.post('auth/login', {
      json: {
        ...credentials,
        device_uuid: deviceUuid,
      },
    });
    return response.json();
  },

  register: async (userData: Omit<RegisterRequest, 'device_uuid'>): Promise<AuthResponse> => {
    const deviceUuid = getDeviceUuid();
    const response = await api.post('auth/register', {
      json: {
        ...userData,
        device_uuid: deviceUuid,
      },
    });
    return response.json();
  },

  logout: async (): Promise<void> => {
    try {
      await authenticatedApi.post('auth/logout');
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
    } finally {
      // Limpar dados locais independentemente do resultado
      localStorage.removeItem('auth_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('user_data');
    }
  },

  refreshToken: async (): Promise<AuthResponse> => {
    const refreshToken = localStorage.getItem('refresh_token');
    if (!refreshToken) {
      throw new Error('Refresh token não encontrado');
    }

    const response = await api.post('auth/refresh', {
      json: { refresh_token: refreshToken },
    });
    return response.json();
  },
};

// Utilitários para gerenciar tokens
export const tokenUtils = {
  setTokens: (authResponse: AuthResponse) => {
    localStorage.setItem('auth_token', authResponse.access_token);
    localStorage.setItem('refresh_token', authResponse.refresh_token);
    localStorage.setItem('user_data', JSON.stringify(authResponse.user));
  },

  clearTokens: () => {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user_data');
  },

  getUser: () => {
    const userData = localStorage.getItem('user_data');
    return userData ? JSON.parse(userData) : null;
  },

  isAuthenticated: (): boolean => {
    return !!getToken();
  },
};
