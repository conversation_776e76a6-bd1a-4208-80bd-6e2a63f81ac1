module.exports = {
  apps: [{
    name: 'dupli-mcp-server',
    script: 'dist/index.js',
    cwd: '/home/<USER>/mcp-server',
    env: {
      NODE_ENV: 'production',
      API_BASE_URL: 'http://localhost:3000',
      N8N_API_KEY: 'your-secret-api-key-here',
      PORT: 3009
    },
    instances: 1,
    exec_mode: 'fork',
    watch: false,
    max_memory_restart: '1G',
    log_file: '/var/log/dupli-mcp-server.log',
    out_file: '/var/log/dupli-mcp-server-out.log',
    error_file: '/var/log/dupli-mcp-server-error.log',
    restart_delay: 4000,
    kill_timeout: 5000,
    wait_ready: false,
    listen_timeout: 10000,
    // Configurações adicionais para produção
    node_args: '--max-old-space-size=1024',
    merge_logs: true,
    time: true
  }]
};
