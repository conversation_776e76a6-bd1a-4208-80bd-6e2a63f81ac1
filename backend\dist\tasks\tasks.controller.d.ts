import { TasksService } from './tasks.service';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
import { TaskResponseDto, TaskListResponseDto } from './dto/task-response.dto';
import { CreateTaskCategoryDto } from './dto/create-task-category.dto';
import { UpdateTaskCategoryDto } from './dto/update-task-category.dto';
import { TaskCategoryResponseDto } from './dto/task-category-response.dto';
export declare class TasksController {
    private readonly tasksService;
    private readonly logger;
    constructor(tasksService: TasksService);
    create(createTaskDto: CreateTaskDto, req: any): Promise<TaskResponseDto>;
    findAll(req: any, page?: number, limit?: number): Promise<TaskListResponseDto>;
    findOne(id: number, req: any): Promise<TaskResponseDto>;
    update(id: number, updateTaskDto: UpdateTaskDto, req: any): Promise<TaskResponseDto>;
    remove(id: number, req: any): Promise<{
        message: string;
    }>;
    complete(id: number, req: any): Promise<TaskResponseDto>;
    private getUserTimezone;
    createCategory(createCategoryDto: CreateTaskCategoryDto, req: any): Promise<TaskCategoryResponseDto>;
    findAllCategories(req: any): Promise<TaskCategoryResponseDto[]>;
    findOneCategory(id: number, req: any): Promise<TaskCategoryResponseDto>;
    updateCategory(id: number, updateCategoryDto: UpdateTaskCategoryDto, req: any): Promise<TaskCategoryResponseDto>;
    removeCategory(id: number, req: any): Promise<{
        message: string;
    }>;
}
