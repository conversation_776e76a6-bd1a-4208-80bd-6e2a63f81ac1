import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider } from './contexts/AuthContext';
import { queryClient } from './lib/query-client';
import ProtectedRoute from './components/ProtectedRoute';
import PublicRoute from './components/PublicRoute';
import Navigation from './components/Navigation';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import Dashboard from './pages/Dashboard';
import TasksPage from './pages/TasksPage';
import WeekView from './pages/WeekView';
import FinancesPage from './pages/FinancesPage';
import IncomePage from './pages/IncomePage';
import ExpensesPage from './pages/ExpensesPage';
import SavingsPage from './pages/SavingsPage';
import CofrinhoPage from './pages/CofrinhoPage';
import CategoriesPage from './pages/CategoriesPage';
import IdeasPage from './pages/IdeasPage';
import IdeaDetailPage from './pages/IdeaDetailPage';
import ProfilePage from './pages/ProfilePage';
import MonthlyProgressPage from './pages/MonthlyProgressPage';
import AnnualFinancialOverview from './pages/AnnualFinancialOverview';

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <Routes>
          {/* Rotas públicas */}
          <Route
            path="/login"
            element={
              <PublicRoute>
                <LoginPage />
              </PublicRoute>
            }
          />
          <Route
            path="/register"
            element={
              <PublicRoute>
                <RegisterPage />
              </PublicRoute>
            }
          />

          {/* Rotas protegidas */}
          <Route
            path="/*"
            element={
              <ProtectedRoute>
                <div className="min-h-screen bg-[#F7F7F7]">
                  <Navigation />
                  <div className="md:pt-[72px]">
                    <Routes>
                      <Route path="/" element={<Navigate to="/dashboard" replace />} />
                      <Route path="/dashboard" element={<Dashboard />} />
                      <Route path="/tasks" element={<TasksPage />} />
                      <Route path="/tasks/week" element={<WeekView />} />
                      <Route path="/finances" element={<FinancesPage />} />
                      <Route path="/finances/income" element={<IncomePage />} />
                      <Route path="/finances/expenses" element={<ExpensesPage />} />
                      <Route path="/finances/savings" element={<SavingsPage />} />
                      <Route path="/finances/cofrinho" element={<CofrinhoPage />} />
                      <Route path="/finances/categories" element={<CategoriesPage />} />
                      <Route path="/finances/annual" element={<AnnualFinancialOverview />} />
                      <Route path="/ideas" element={<IdeasPage />} />
                      <Route path="/ideas/:id" element={<IdeaDetailPage />} />
                      <Route path="/profile" element={<ProfilePage />} />
                      <Route path="/progresso-mensal" element={<MonthlyProgressPage />} />
                    </Routes>
                  </div>
                </div>
              </ProtectedRoute>
            }
          />
        </Routes>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;