"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var DashboardService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DashboardService = void 0;
const common_1 = require("@nestjs/common");
const database_types_1 = require("../database.types");
const timezone_1 = require("../utils/timezone");
const dashboard_exceptions_1 = require("./exceptions/dashboard.exceptions");
let DashboardService = DashboardService_1 = class DashboardService {
    logger = new common_1.Logger(DashboardService_1.name);
    db = database_types_1.db;
    async getDashboardData(userId) {
        try {
            this.logger.log(`Buscando dados do dashboard para usuário ${userId}`);
            const user = await this.getUserInfo(userId);
            const [tasks, finances, ideas, monthlyProgress] = await Promise.all([
                this.getTasksSummary(userId, user.timezone),
                this.getFinancesSummary(userId, user.timezone),
                this.getIdeasSummary(userId, user.timezone),
                this.getMonthlyProgress(userId, user.timezone)
            ]);
            const currentDate = new Date();
            const dashboardData = {
                user,
                tasks,
                finances,
                ideas,
                monthlyProgress,
                currentMonth: currentDate.toLocaleString('pt-BR', { month: 'long' }),
                currentYear: currentDate.getFullYear()
            };
            this.logger.log(`Dados do dashboard carregados com sucesso para usuário ${userId}`);
            return dashboardData;
        }
        catch (error) {
            this.logger.error(`Erro ao buscar dados do dashboard para usuário ${userId}:`, error);
            if (error instanceof dashboard_exceptions_1.UserNotFoundException) {
                throw error;
            }
            throw new dashboard_exceptions_1.DashboardDataException(error.message);
        }
    }
    async getUserInfo(userId) {
        try {
            const user = await this.db
                .selectFrom('users')
                .select(['name', 'timezone'])
                .where('id', '=', userId)
                .where('deleted_at', 'is', null)
                .executeTakeFirst();
            if (!user) {
                throw new dashboard_exceptions_1.UserNotFoundException(userId);
            }
            return {
                name: user.name,
                timezone: user.timezone
            };
        }
        catch (error) {
            if (error instanceof dashboard_exceptions_1.UserNotFoundException) {
                throw error;
            }
            this.logger.error(`Erro ao buscar informações do usuário ${userId}:`, error);
            throw new dashboard_exceptions_1.DashboardDataException(`Erro ao buscar informações do usuário: ${error.message}`);
        }
    }
    async getTasksSummary(userId, userTimezone) {
        try {
            const tasks = await this.db
                .selectFrom('tasks')
                .select(['completed_at'])
                .where('user_id', '=', userId)
                .execute();
            const completed = tasks.filter(task => task.completed_at !== null).length;
            const total = tasks.length;
            this.logger.debug(`Resumo de tarefas para usuário ${userId}: ${completed}/${total} completas`);
            return {
                completed,
                total
            };
        }
        catch (error) {
            this.logger.error(`Erro ao buscar resumo de tarefas para usuário ${userId}:`, error);
            throw new dashboard_exceptions_1.DashboardDataException(`Erro ao buscar resumo de tarefas: ${error.message}`);
        }
    }
    async getFinancesSummary(userId, userTimezone) {
        try {
            const currentDate = new Date();
            const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
            const endOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0, 23, 59, 59);
            const startOfMonthUTC = (0, timezone_1.fromUserTimezone)(startOfMonth, userTimezone);
            const endOfMonthUTC = (0, timezone_1.fromUserTimezone)(endOfMonth, userTimezone);
            const transactions = await this.db
                .selectFrom('finances')
                .select(['transaction_type', 'amount', 'is_saving'])
                .where('user_id', '=', userId)
                .where('transaction_date', '>=', startOfMonthUTC)
                .where('transaction_date', '<=', endOfMonthUTC)
                .execute();
            let income = 0;
            let expenses = 0;
            let savings = 0;
            transactions.forEach(transaction => {
                const amount = parseFloat(transaction.amount);
                if (transaction.transaction_type === 'income') {
                    income += amount;
                }
                else if (transaction.transaction_type === 'expense') {
                    expenses += amount;
                    if (transaction.is_saving) {
                        savings += amount;
                    }
                }
            });
            const savingsGoal = await this.db
                .selectFrom('config_annual_savings_goal')
                .select(['amount'])
                .where('user_id', '=', userId)
                .where('year', '=', currentDate.getFullYear())
                .executeTakeFirst();
            const monthlyBudget = savingsGoal ? parseFloat(savingsGoal.amount) / 12 : 0;
            const hasAnnualGoal = !!savingsGoal;
            this.logger.debug(`Resumo financeiro para usuário ${userId}: receita=${income}, gastos=${expenses}, economia=${savings}, meta anual=${hasAnnualGoal}`);
            return {
                spent: expenses,
                budget: monthlyBudget,
                income,
                expenses,
                savings,
                hasAnnualGoal
            };
        }
        catch (error) {
            this.logger.error(`Erro ao buscar resumo financeiro para usuário ${userId}:`, error);
            throw new dashboard_exceptions_1.DashboardDataException(`Erro ao buscar resumo financeiro: ${error.message}`);
        }
    }
    async getIdeasSummary(userId, userTimezone) {
        const currentDate = new Date();
        const startOfDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate());
        const endOfDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate(), 23, 59, 59);
        const startOfDayUTC = (0, timezone_1.fromUserTimezone)(startOfDay, userTimezone);
        const endOfDayUTC = (0, timezone_1.fromUserTimezone)(endOfDay, userTimezone);
        const todayIdeas = await this.db
            .selectFrom('ideas')
            .select(['id'])
            .where('user_id', '=', userId)
            .where('created_at', '>=', startOfDayUTC)
            .where('created_at', '<=', endOfDayUTC)
            .execute();
        const totalIdeas = await this.db
            .selectFrom('ideas')
            .select(['id'])
            .where('user_id', '=', userId)
            .execute();
        const favoriteIdeas = await this.db
            .selectFrom('ideas')
            .select(['id'])
            .where('user_id', '=', userId)
            .where('is_favorite', '=', true)
            .execute();
        return {
            today: todayIdeas.length,
            total: totalIdeas.length,
            favorites: favoriteIdeas.length
        };
    }
    async getMonthlyProgress(userId, userTimezone) {
        const currentDate = new Date();
        const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
        const endOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0, 23, 59, 59);
        const startOfMonthUTC = (0, timezone_1.fromUserTimezone)(startOfMonth, userTimezone);
        const endOfMonthUTC = (0, timezone_1.fromUserTimezone)(endOfMonth, userTimezone);
        const appointments = await this.db
            .selectFrom('tasks')
            .select(['completed_at'])
            .where('user_id', '=', userId)
            .where('task_type', '=', 'appointment')
            .where('created_at', '>=', startOfMonthUTC)
            .where('created_at', '<=', endOfMonthUTC)
            .execute();
        const appointmentsCompleted = appointments.filter(task => task.completed_at !== null).length;
        const appointmentsTotal = appointments.length;
        const savingsTransactions = await this.db
            .selectFrom('finances')
            .select(['amount'])
            .where('user_id', '=', userId)
            .where('transaction_type', '=', 'expense')
            .where('is_saving', '=', true)
            .where('transaction_date', '>=', startOfMonthUTC)
            .where('transaction_date', '<=', endOfMonthUTC)
            .execute();
        const monthlySavings = savingsTransactions.reduce((total, transaction) => {
            return total + parseFloat(transaction.amount);
        }, 0);
        const monthlyIdeas = await this.db
            .selectFrom('ideas')
            .select(['id'])
            .where('user_id', '=', userId)
            .where('created_at', '>=', startOfMonthUTC)
            .where('created_at', '<=', endOfMonthUTC)
            .execute();
        const completedTasks = await this.db
            .selectFrom('tasks')
            .select(['id'])
            .where('user_id', '=', userId)
            .where('completed_at', '>=', startOfMonthUTC)
            .where('completed_at', '<=', endOfMonthUTC)
            .execute();
        const savingsGoal = await this.db
            .selectFrom('config_annual_savings_goal')
            .select(['amount'])
            .where('user_id', '=', userId)
            .where('year', '=', currentDate.getFullYear())
            .executeTakeFirst();
        const monthlyGoal = savingsGoal ? parseFloat(savingsGoal.amount) / 12 : 0;
        const financialGoalProgress = monthlyGoal > 0 ? (monthlySavings / monthlyGoal) * 100 : 0;
        return {
            appointments: {
                completed: appointmentsCompleted,
                total: appointmentsTotal
            },
            savings: monthlySavings,
            ideas: monthlyIdeas.length,
            tasksCompleted: completedTasks.length,
            financialGoalProgress: Math.min(financialGoalProgress, 100)
        };
    }
};
exports.DashboardService = DashboardService;
exports.DashboardService = DashboardService = DashboardService_1 = __decorate([
    (0, common_1.Injectable)()
], DashboardService);
//# sourceMappingURL=dashboard.service.js.map