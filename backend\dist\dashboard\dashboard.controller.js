"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var DashboardController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DashboardController = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const dashboard_service_1 = require("./dashboard.service");
const dashboard_exceptions_1 = require("./exceptions/dashboard.exceptions");
let DashboardController = DashboardController_1 = class DashboardController {
    dashboardService;
    logger = new common_1.Logger(DashboardController_1.name);
    constructor(dashboardService) {
        this.dashboardService = dashboardService;
    }
    async getDashboard(req) {
        try {
            const userId = req.user?.userId;
            if (!userId) {
                this.logger.warn('Tentativa de acesso ao dashboard sem userId válido');
                throw new dashboard_exceptions_1.InvalidUserTokenException();
            }
            this.logger.log(`Solicitação de dados do dashboard para usuário ${userId}`);
            return await this.dashboardService.getDashboardData(userId);
        }
        catch (error) {
            if (error instanceof dashboard_exceptions_1.UserNotFoundException ||
                error instanceof dashboard_exceptions_1.DashboardDataException ||
                error instanceof dashboard_exceptions_1.InvalidUserTokenException) {
                throw error;
            }
            this.logger.error('Erro inesperado ao buscar dados do dashboard:', error);
            throw new common_1.HttpException('Erro interno do servidor ao buscar dados do dashboard', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.DashboardController = DashboardController;
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DashboardController.prototype, "getDashboard", null);
exports.DashboardController = DashboardController = DashboardController_1 = __decorate([
    (0, common_1.Controller)('dashboard'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    __metadata("design:paramtypes", [dashboard_service_1.DashboardService])
], DashboardController);
//# sourceMappingURL=dashboard.controller.js.map