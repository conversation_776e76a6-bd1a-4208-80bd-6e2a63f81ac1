import { Generated, Kysely } from "kysely";
export interface UserTable {
    id: Generated<number>;
    name: string;
    email: string;
    password?: string | null;
    phone?: string | null;
    timezone: string;
    created_at: Date;
    updated_at: Date;
    deleted_at?: Date | null;
}
export interface RefreshTokenTable {
    id: Generated<number>;
    refresh_token: string;
    device_uuid: string;
    expires_at: Date;
    revoked?: boolean | null;
    user_id?: number | null;
    created_at: Date;
    updated_at: Date;
}
export interface ConfigAnnualSavingsGoalTable {
    id: Generated<number>;
    year: number;
    amount: string;
    user_id?: number | null;
    created_at: Date;
    updated_at: Date;
}
export interface TaskCategoryTable {
    id: Generated<number>;
    name: string;
    user_id?: number | null;
    created_at: Date;
    updated_at: Date;
}
export interface TaskTable {
    id: Generated<number>;
    task_type: 'appointment' | 'task';
    category_id?: number | null;
    name: string;
    description?: string | null;
    task_date?: Date | null;
    user_id: number;
    completed_at?: Date | null;
    created_at: Date;
    updated_at: Date;
}
export interface FinanceCategoryTable {
    id: Generated<number>;
    name: string;
    transaction_type: 'income' | 'expense';
    color?: string | null;
    user_id?: number | null;
    created_at: Date;
    updated_at: Date;
}
export interface FinanceTable {
    id: Generated<number>;
    transaction_type: 'income' | 'expense';
    category_id?: number | null;
    is_saving?: boolean | null;
    description?: string | null;
    amount: string;
    transaction_date: Date;
    user_id: number;
    created_at: Date;
    updated_at: Date;
}
export interface IdeaCategoryTable {
    id: Generated<number>;
    name: string;
    user_id?: number | null;
    created_at: Date;
    updated_at: Date;
}
export interface IdeaTable {
    id: Generated<number>;
    category_id?: number | null;
    name: string;
    description?: string | null;
    content?: string | null;
    is_favorite?: boolean | null;
    user_id: number;
    created_at: Date;
    updated_at: Date;
}
export interface IntegrationWhatsappTable {
    id: Generated<number>;
    status: 'pending' | 'active' | 'inactive';
    phone: string;
    is_validated?: boolean | null;
    user_id: number;
}
export interface Database {
    users: UserTable;
    refresh_tokens: RefreshTokenTable;
    config_annual_savings_goal: ConfigAnnualSavingsGoalTable;
    tasks_categories: TaskCategoryTable;
    tasks: TaskTable;
    finances_categories: FinanceCategoryTable;
    finances: FinanceTable;
    ideas_categories: IdeaCategoryTable;
    ideas: IdeaTable;
    integrations_whatsapp: IntegrationWhatsappTable;
}
export declare const db: Kysely<Database>;
