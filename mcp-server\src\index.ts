#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { SSEServerTransport } from '@modelcontextprotocol/sdk/server/sse.js';
import { CallToolRequestSchema, ListToolsRequestSchema } from '@modelcontextprotocol/sdk/types.js';
import axios from 'axios';
import { z } from 'zod';
import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';

dotenv.config();

// Configurações
const CONFIG = {
  API_BASE_URL: process.env.API_BASE_URL || 'http://localhost:3000',
  API_KEY: process.env.N8N_API_KEY || 'your-secret-api-key-here',
  MCP_SERVER: {
    name: 'dupli-agentwpp',
    version: '1.0.0',
  }
};

// Cliente HTTP
const apiClient = axios.create({
  baseURL: CONFIG.API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': CONFIG.API_KEY
  },
  timeout: 10000
});

// Schemas de validação
const PhoneSchema = z.string().regex(/^\d{10,15}$/, 'Número de telefone deve conter entre 10 e 15 dígitos');

// Tools disponíveis
const tools = [
  // Integration tools
  {
    name: 'check_integration',
    description: 'Verificar se o telefone possui integração WhatsApp ativa',
    inputSchema: {
      type: 'object',
      properties: {
        phone: { type: 'string', description: 'Número de telefone do usuário' }
      },
      required: ['phone']
    }
  },
  {
    name: 'get_dashboard',
    description: 'Obter dados do dashboard do usuário',
    inputSchema: {
      type: 'object',
      properties: {
        phone: { type: 'string', description: 'Número de telefone do usuário' }
      },
      required: ['phone']
    }
  },
  // Task tools
  {
    name: 'create_task',
    description: 'Criar uma nova tarefa',
    inputSchema: {
      type: 'object',
      properties: {
        phone: { type: 'string', description: 'Número de telefone do usuário' },
        task_type: { type: 'string', enum: ['appointment', 'task'], description: 'Tipo da tarefa' },
        category_id: { type: 'number', description: 'ID da categoria (opcional)' },
        name: { type: 'string', description: 'Nome da tarefa' },
        description: { type: 'string', description: 'Descrição da tarefa (opcional)' },
        task_date: { type: 'string', description: 'Data da tarefa no formato ISO (opcional)' }
      },
      required: ['phone', 'task_type', 'name']
    }
  },
  {
    name: 'list_tasks',
    description: 'Listar tarefas do usuário com opções de filtro e busca',
    inputSchema: {
      type: 'object',
      properties: {
        phone: { type: 'string', description: 'Número de telefone do usuário' },
        page: { type: 'number', description: 'Número da página (padrão: 1)' },
        limit: { type: 'number', description: 'Limite de itens por página (padrão: 10)' },
        search: { type: 'string', description: 'Texto para buscar no nome ou descrição das tarefas' },
        start_date: { type: 'string', description: 'Data de início para filtro (formato ISO)' },
        end_date: { type: 'string', description: 'Data de fim para filtro (formato ISO)' },
        completed: { type: 'boolean', description: 'Filtrar por tarefas concluídas (true) ou pendentes (false)' },
        category_id: { type: 'number', description: 'Filtrar por ID da categoria' }
      },
      required: ['phone']
    }
  },
];

// Handlers para os tools
const handlers = {
  // Integration handlers
  async check_integration(args: any) {
    const { phone } = args;
    const response = await apiClient.get(`/agentwpp/check-integration/${phone}`);
    return response.data;
  },

  async get_dashboard(args: any) {
    const { phone } = args;
    const response = await apiClient.get(`/agentwpp/dashboard/${phone}`);
    return response.data;
  },

  // Task handlers
  async create_task(args: any) {
    const response = await apiClient.post('/agentwpp/tasks', args);
    return response.data;
  },

  async list_tasks(args: any) {
    const { phone, page = 1, limit = 10, search, start_date, end_date, completed, category_id } = args;
    
    // Construir parâmetros de query
    const params: any = { page, limit };
    if (search) params.search = search;
    if (start_date) params.start_date = start_date;
    if (end_date) params.end_date = end_date;
    if (completed !== undefined) params.completed = completed;
    if (category_id) params.category_id = category_id;
    
    const response = await apiClient.get(`/agentwpp/tasks/${phone}`, { params });
    return response.data;
  },
};

// Servidor MCP usando SDK oficial
class DupliMCPServer {
  private server: Server;

  constructor() {
    console.log('🔧 Initializing MCP Server...');

    this.server = new Server({
      name: CONFIG.MCP_SERVER.name,
      version: CONFIG.MCP_SERVER.version
    });

    console.log('📋 Setting up tool handlers...');
    this.setupHandlers();
    console.log('✅ MCP Server constructor completed');
  }

  private setupHandlers() {
    console.log(`🛠️ Registering ${tools.length} tools...`);

    // List tools handler
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      console.log('📋 Tools list requested, returning tools:', tools.map(t => t.name));
      return { tools };
    });

    this.setupSessionHandlers(this.server);
  }

  private setupSessionHandlers(server: Server) {
    // List tools handler
    server.setRequestHandler(ListToolsRequestSchema, async () => {
      console.log('📋 Session tools list requested, returning tools:', tools.map(t => t.name));
      return { tools };
    });

    // Call tool handler
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;
      
      if (!handlers[name as keyof typeof handlers]) {
        throw new Error(`Unknown tool: ${name}`);
      }

      try {
        const result = await handlers[name as keyof typeof handlers](args);
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(result, null, 2)
            }
          ]
        };
      } catch (error) {
        console.error(`Error in tool ${name}:`, error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return {
          content: [
            {
              type: 'text',
              text: `Error: ${errorMessage}`
            }
          ],
          isError: true
        };
      }
    });
  }

  async run() {
    console.log('🚀 Iniciando MCP Server...');
    const app = express();

    const port = process.env.PORT ? parseInt(process.env.PORT) : 3001;

    // Middleware JSON DEVE vir antes das rotas que precisam dele
    app.use(express.json());

    // MCP endpoint - Usar apenas uma rota que funciona com o SDK
    app.all('/mcp', async (req, res) => {
      console.log(`MCP ${req.method} connection from ${req.ip}`);

      try {
        // Para OPTIONS, configurar CORS e retornar
        if (req.method === 'OPTIONS') {
          res.setHeader('Access-Control-Allow-Origin', '*');
          res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Cache-Control, Accept, Authorization');
          res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
          res.setHeader('Access-Control-Max-Age', '86400');
          res.status(200).end();
          return;
        }

        console.log('Setting up MCP connection...');

        // Configurar headers CORS manualmente
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Cache-Control, Accept, Authorization');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');

        // Criar transport SSE - ele vai lidar com GET e POST automaticamente
        const transport = new SSEServerTransport('/mcp', res);

        // Conectar servidor MCP principal
        await this.server.connect(transport);
        console.log('✅ MCP server connected to transport successfully');

        // Configurar cleanup quando conexão fechar
        res.on('close', () => {
          console.log('🔌 MCP connection closed');
        });

        res.on('error', (error) => {
          console.error('❌ MCP connection error:', error);
        });

      } catch (error: any) {
        console.error('❌ Error setting up MCP transport:', error);
        if (!res.headersSent) {
          res.status(500).json({
            error: error.message,
            stack: error.stack
          });
        }
      }
    });



    // Configurar CORS para outras rotas (APÓS a rota MCP)
    app.use(cors({
      origin: '*',
      credentials: false,
      methods: ['GET', 'POST', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'Accept', 'Cache-Control'],
      exposedHeaders: ['Content-Type']
    }));

    // Health check endpoint
    app.get('/health', (_req, res) => {
      res.json({ status: 'ok', timestamp: new Date().toISOString() });
    });



    app.listen(port, '0.0.0.0', () => {
      console.log(`✅ Dupli MCP Server running on http://0.0.0.0:${port}`);
      console.log(`🔗 MCP Endpoint: http://0.0.0.0:${port}/mcp`);
      console.log(`🏥 Health check: http://0.0.0.0:${port}/health`);
      console.log(`📋 MCP Server initialized with tools`);
      console.log(`🎯 Ready for MCP Inspector connections!`);
    });
  }
}

// Executar servidor
async function main() {
  const server = new DupliMCPServer();
  await server.run();
}

main().catch(console.error);
