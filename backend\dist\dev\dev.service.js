"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DevService = void 0;
const common_1 = require("@nestjs/common");
const kysely_1 = require("kysely");
const nestjs_kysely_1 = require("nestjs-kysely");
let DevService = class DevService {
    db;
    constructor(db) {
        this.db = db;
    }
    async getOrCreateTestUser() {
        let user = await this.db
            .selectFrom('users')
            .selectAll()
            .where('email', '=', '<EMAIL>')
            .executeTakeFirst();
        if (!user) {
            const result = await this.db
                .insertInto('users')
                .values({
                name: 'Dev User',
                email: '<EMAIL>',
                password_hash: 'dev-password-hash',
                phone: '+5511999999999',
                timezone: 'America/Sao_Paulo',
                created_at: new Date(),
                updated_at: new Date()
            })
                .executeTakeFirst();
            user = await this.db
                .selectFrom('users')
                .selectAll()
                .where('id', '=', Number(result.insertId))
                .executeTakeFirstOrThrow();
        }
        return user;
    }
    async seedTestData(userId) {
        const existingCategories = await this.db
            .selectFrom('finance_categories')
            .selectAll()
            .where('user_id', '=', userId)
            .execute();
        if (existingCategories.length === 0) {
            const categories = [
                { name: 'Alimentação', transaction_type: 'expense', color: '#B4EB00' },
                { name: 'Transporte', transaction_type: 'expense', color: '#212121' },
                { name: 'Moradia', transaction_type: 'expense', color: '#6C6C6C' },
                { name: 'Lazer', transaction_type: 'expense', color: '#BBBBBB' },
                { name: 'Saúde', transaction_type: 'expense', color: '#E5E7EB' },
                { name: 'Outros', transaction_type: 'expense', color: '#9CA3AF' },
                { name: 'Salário', transaction_type: 'income', color: '#4CAF50' },
                { name: 'Freelance', transaction_type: 'income', color: '#2196F3' }
            ];
            for (const category of categories) {
                await this.db
                    .insertInto('finance_categories')
                    .values({
                    ...category,
                    user_id: userId,
                    created_at: new Date(),
                    updated_at: new Date()
                })
                    .execute();
            }
        }
        const existingTransactions = await this.db
            .selectFrom('finances')
            .selectAll()
            .where('user_id', '=', userId)
            .execute();
        if (existingTransactions.length === 0) {
            const categories = await this.db
                .selectFrom('finance_categories')
                .selectAll()
                .where('user_id', '=', userId)
                .execute();
            const transactions = [
                { amount: '5000.00', description: 'Salário', transaction_type: 'income', category_id: categories.find(c => c.name === 'Salário')?.id },
                { amount: '800.00', description: 'Supermercado', transaction_type: 'expense', category_id: categories.find(c => c.name === 'Alimentação')?.id },
                { amount: '300.00', description: 'Uber', transaction_type: 'expense', category_id: categories.find(c => c.name === 'Transporte')?.id },
                { amount: '1200.00', description: 'Aluguel', transaction_type: 'expense', category_id: categories.find(c => c.name === 'Moradia')?.id },
                { amount: '500.00', description: 'Poupança', transaction_type: 'expense', category_id: categories.find(c => c.name === 'Outros')?.id, is_saving: true }
            ];
            for (const transaction of transactions) {
                if (transaction.category_id) {
                    await this.db
                        .insertInto('finances')
                        .values({
                        ...transaction,
                        user_id: userId,
                        transaction_date: new Date(),
                        created_at: new Date(),
                        updated_at: new Date()
                    })
                        .execute();
                }
            }
        }
        return { message: 'Dados de teste criados com sucesso' };
    }
};
exports.DevService = DevService;
exports.DevService = DevService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, nestjs_kysely_1.InjectKysely)()),
    __metadata("design:paramtypes", [kysely_1.Kysely])
], DevService);
//# sourceMappingURL=dev.service.js.map