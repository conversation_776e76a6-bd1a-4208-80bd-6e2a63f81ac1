"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const bcrypt = require("bcrypt");
const uuid_1 = require("uuid");
const database_types_1 = require("../database.types");
let AuthService = class AuthService {
    jwtService;
    db = database_types_1.db;
    constructor(jwtService) {
        this.jwtService = jwtService;
    }
    async register(registerDto) {
        const { name, email, password, phone, timezone } = registerDto;
        const timezoneUser = timezone || 'America/Sao_Paulo';
        const existingUser = await this.db
            .selectFrom('users')
            .selectAll()
            .where('email', '=', email)
            .executeTakeFirst();
        if (existingUser) {
            throw new Error('O usuário já existe');
        }
        const hashedPassword = await bcrypt.hash(password, 10);
        const user = await this.db
            .insertInto('users')
            .values({
            name,
            email,
            phone: phone || null,
            password: hashedPassword,
            timezone: timezoneUser,
            created_at: new Date(),
            updated_at: new Date(),
        })
            .executeTakeFirst();
        if (!user) {
            throw new Error('Erro ao adicionar o usuário');
        }
        const userId = parseInt(user.insertId);
        const defaultTaskCategories = [
            'Reunião', 'Compromisso', 'Tarefa', 'Responsabilidade', 'Agenda', 'Chamada'
        ];
        for (const categoryName of defaultTaskCategories) {
            await this.db
                .insertInto('tasks_categories')
                .values({
                name: categoryName,
                user_id: userId,
                created_at: new Date(),
                updated_at: new Date(),
            })
                .execute();
        }
        const defaultFinanceCategories = [
            { name: 'Fixo', type: 'income', color: 'rgb(229, 231, 235)' },
            { name: 'Variável', type: 'income', color: 'rgb(229, 231, 235)' },
            { name: 'Investimentos', type: 'income', color: 'rgb(229, 231, 235)' },
            { name: 'Outros', type: 'income', color: 'rgb(229, 231, 235)' },
            { name: 'Alimentação', type: 'expense', color: 'rgb(180, 235, 0)' },
            { name: 'Transporte', type: 'expense', color: 'rgb(33, 33, 33)' },
            { name: 'Lazer', type: 'expense', color: 'rgb(108, 108, 108)' },
            { name: 'Moradia', type: 'expense', color: 'rgb(187, 187, 187)' },
            { name: 'Outros', type: 'expense', color: 'rgb(229, 231, 235)' }
        ];
        for (const category of defaultFinanceCategories) {
            await this.db
                .insertInto('finances_categories')
                .values({
                name: category.name,
                transaction_type: category.type === 'expense' ? 'expense' : 'income',
                color: category.color,
                user_id: userId,
                created_at: new Date(),
                updated_at: new Date(),
            })
                .execute();
        }
        const defaultIdeaCategories = [
            { name: 'App' },
            { name: 'Projeto pessoal' },
            { name: 'Estudo' },
            { name: 'Negócio' },
            { name: 'Outro' },
        ];
        for (const category of defaultIdeaCategories) {
            await this.db
                .insertInto('ideas_categories')
                .values({
                name: category.name,
                user_id: userId,
                created_at: new Date(),
                updated_at: new Date(),
            })
                .execute();
        }
        return {
            status: "success",
            data: []
        };
    }
    async validateUser(email, password) {
        const user = await this.db
            .selectFrom('users')
            .select(['id', 'email', 'password', 'name', 'phone', 'timezone'])
            .where('email', '=', email)
            .executeTakeFirst();
        if (user && user.password && await bcrypt.compare(password, user.password)) {
            return user;
        }
        return null;
    }
    async login(email, password, deviceUuid) {
        const user = await this.validateUser(email, password);
        if (!user) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        const device_uuid = deviceUuid || (0, uuid_1.v4)();
        const payload = { sub: user.id, email: user.email };
        const accessToken = this.jwtService.sign(payload);
        const refreshToken = (0, uuid_1.v4)();
        const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
        const hashedRefreshToken = await bcrypt.hash(refreshToken, 10);
        await this.db
            .insertInto('refresh_tokens')
            .values({
            user_id: parseInt(user.id.toString()),
            refresh_token: hashedRefreshToken,
            device_uuid,
            expires_at: expiresAt,
            revoked: false,
            created_at: new Date(),
            updated_at: new Date(),
        })
            .execute();
        return {
            status: "success",
            data: {
                access_token: accessToken,
                refresh_token: refreshToken,
                device_uuid,
                user: {
                    id: user.id,
                    name: user.name,
                    email: user.email,
                    phone: user.phone || null,
                    timezone: user.timezone,
                }
            }
        };
    }
    async refresh(refreshToken, deviceUuid) {
        const token = await this.db
            .selectFrom('refresh_tokens')
            .selectAll()
            .where('device_uuid', '=', deviceUuid)
            .where('revoked', '=', false)
            .where('expires_at', '>', new Date())
            .orderBy('id', 'desc')
            .executeTakeFirst();
        if (!token || !(await bcrypt.compare(refreshToken, token.refresh_token))) {
            throw new common_1.UnauthorizedException('Invalid refresh token or device UUID');
        }
        const user = await this.db
            .selectFrom('users')
            .select(['id', 'email'])
            .where('id', '=', token.user_id)
            .executeTakeFirst();
        if (!user) {
            throw new common_1.UnauthorizedException('User not found');
        }
        const payload = { sub: user.id, email: user.email };
        const newAccessToken = this.jwtService.sign(payload);
        const newRefreshToken = (0, uuid_1.v4)();
        const hashedNewRefreshToken = await bcrypt.hash(newRefreshToken, 10);
        await this.db
            .updateTable('refresh_tokens')
            .set({
            refresh_token: hashedNewRefreshToken,
            expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        })
            .where('id', '=', token.id)
            .execute();
        return {
            status: "success",
            data: {
                access_token: newAccessToken,
                refresh_token: newRefreshToken,
                device_uuid: token.device_uuid
            }
        };
    }
    async logout(refreshToken, deviceUuid) {
        const token = await this.db
            .selectFrom('refresh_tokens')
            .selectAll()
            .where('device_uuid', '=', deviceUuid)
            .where('revoked', '=', false)
            .executeTakeFirst();
        if (token && await bcrypt.compare(refreshToken, token.refresh_token)) {
            await this.db
                .updateTable('refresh_tokens')
                .set({ revoked: true })
                .where('id', '=', token.id)
                .execute();
        }
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [jwt_1.JwtService])
], AuthService);
//# sourceMappingURL=auth.service.js.map