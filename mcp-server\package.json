{"name": "dupli-mcp-server", "version": "1.0.0", "description": "MCP Server para integração com Dupli AgentWPP", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx src/index.ts"}, "dependencies": {"@modelcontextprotocol/sdk": "^0.4.0", "axios": "^1.6.0", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.18.0", "zod": "^3.22.0"}, "devDependencies": {"@types/cors": "^2.8.0", "@types/express": "^4.17.0", "@types/node": "^20.0.0", "tsx": "^4.0.0", "typescript": "^5.0.0"}}