import React, { useState } from 'react';
import { ArrowLeft, Filter, X, Download, Target, TrendingUp, TrendingDown, Calendar, BarChart3 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { LineChart, Line, XAxis, YAxis, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';

interface MonthData {
  month: string;
  monthNumber: number;
  income: number;
  expenses: number;
  savings: number;
  balance: number;
}

interface CategoryData {
  name: string;
  value: number;
  color: string;
  percentage: number;
}

interface YearComparison {
  category: string;
  currentYear: number;
  previousYear: number;
  change: number;
}

const mockData: MonthData[] = [
  { month: 'Jan', monthNumber: 1, income: 4500, expenses: 3200, savings: 1300, balance: 1300 },
  { month: 'Fev', monthNumber: 2, income: 4800, expenses: 3100, savings: 1700, balance: 1700 },
  { month: 'Mar', monthNumber: 3, income: 5200, expenses: 3800, savings: 1400, balance: 1400 },
  { month: 'Abr', monthNumber: 4, income: 4800, expenses: 2310, savings: 2490, balance: 2490 },
  { month: 'Mai', monthNumber: 5, income: 5100, expenses: 3400, savings: 1700, balance: 1700 },
  { month: 'Jun', monthNumber: 6, income: 4900, expenses: 3200, savings: 1700, balance: 1700 },
  { month: 'Jul', monthNumber: 7, income: 5300, expenses: 3600, savings: 1700, balance: 1700 },
  { month: 'Ago', monthNumber: 8, income: 5000, expenses: 3300, savings: 1700, balance: 1700 },
  { month: 'Set', monthNumber: 9, income: 5200, expenses: 3500, savings: 1700, balance: 1700 },
  { month: 'Out', monthNumber: 10, income: 5400, expenses: 3700, savings: 1700, balance: 1700 },
  { month: 'Nov', monthNumber: 11, income: 5100, expenses: 3400, savings: 1700, balance: 1700 },
  { month: 'Dez', monthNumber: 12, income: 5500, expenses: 3800, savings: 1700, balance: 1700 }
];

const mockCategoryData: CategoryData[] = [
  { name: 'Alimentação', value: 12800, color: '#B4EB00', percentage: 32 },
  { name: 'Transporte', value: 8400, color: '#212121', percentage: 21 },
  { name: 'Moradia', value: 7200, color: '#6C6C6C', percentage: 18 },
  { name: 'Lazer', value: 4800, color: '#BBBBBB', percentage: 12 },
  { name: 'Saúde', value: 3600, color: '#E5E7EB', percentage: 9 },
  { name: 'Outros', value: 3200, color: '#9CA3AF', percentage: 8 }
];

const mockYearComparison: YearComparison[] = [
  { category: 'Receitas Totais', currentYear: 61600, previousYear: 58200, change: 5.8 },
  { category: 'Despesas Totais', currentYear: 40200, previousYear: 42800, change: -6.1 },
  { category: 'Economias Totais', currentYear: 21400, previousYear: 15400, change: 39.0 },
  { category: 'Saldo Médio', currentYear: 1783, previousYear: 1283, change: 39.0 }
];

const AnnualFinancialOverview: React.FC = () => {
  const navigate = useNavigate();
  const [selectedYear, setSelectedYear] = useState(2024);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [selectedMonth, setSelectedMonth] = useState<string>('all');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'chart' | 'table'>('chart');
  const [isGoalsModalOpen, setIsGoalsModalOpen] = useState(false);
  const [annualGoal, setAnnualGoal] = useState(25000);

  const currentYear = new Date().getFullYear();
  const availableYears = Array.from({ length: 5 }, (_, i) => currentYear - i);

  // Calculate totals and metrics
  const totalIncome = mockData.reduce((sum, month) => sum + month.income, 0);
  const totalExpenses = mockData.reduce((sum, month) => sum + month.expenses, 0);
  const totalSavings = mockData.reduce((sum, month) => sum + month.savings, 0);
  const averageMonthlyIncome = totalIncome / mockData.length;
  const averageMonthlyExpenses = totalExpenses / mockData.length;
  const averageMonthlySavings = totalSavings / mockData.length;
  const savingsRate = (totalSavings / totalIncome) * 100;
  const goalProgress = (totalSavings / annualGoal) * 100;

  // Find best and worst months
  const bestSavingsMonth = mockData.reduce((best, current) => 
    current.savings > best.savings ? current : best
  );
  const worstSavingsMonth = mockData.reduce((worst, current) => 
    current.savings < worst.savings ? current : worst
  );
  const highestIncomeMonth = mockData.reduce((highest, current) => 
    current.income > highest.income ? current : highest
  );
  const highestExpenseMonth = mockData.reduce((highest, current) => 
    current.expenses > highest.expenses ? current : highest
  );

  // Filter data based on selections
  const filteredData = mockData.filter(month => {
    if (selectedMonth !== 'all' && month.month !== selectedMonth) return false;
    return true;
  });

  const handleExportReport = () => {
    // Mock export functionality
    const reportData = {
      year: selectedYear,
      totalIncome,
      totalExpenses,
      totalSavings,
      savingsRate,
      monthlyData: mockData,
      categoryBreakdown: mockCategoryData,
      yearComparison: mockYearComparison
    };
    
    console.log('Exporting report:', reportData);
    alert('Relatório exportado com sucesso! (Funcionalidade simulada)');
  };

  const handleSetGoal = () => {
    setIsGoalsModalOpen(false);
    alert(`Meta anual definida: ${new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(annualGoal)}`);
  };

  return (
    <div className="min-h-screen bg-[#F7F7F7] pb-24 md:pb-6">
      {/* Fixed Header */}
      <div className="fixed top-0 left-0 right-0 bg-[#F7F7F7] z-50">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <button 
              onClick={() => navigate('/finances')}
              className="w-10 h-10 bg-white rounded-full shadow-sm hover:shadow-md transition-shadow flex items-center justify-center"
            >
              <ArrowLeft size={20} className="text-gray-600" />
            </button>
            
            <h1 className="text-xl font-bold">Visão Anual</h1>
            
            <div className="flex items-center gap-2">
              <button
                onClick={() => setIsFilterOpen(true)}
                className="w-10 h-10 bg-white rounded-full shadow-sm hover:shadow-md transition-shadow flex items-center justify-center"
              >
                <Filter size={20} className="text-gray-600" />
              </button>
              <button
                onClick={handleExportReport}
                className="w-10 h-10 bg-white rounded-full shadow-sm hover:shadow-md transition-shadow flex items-center justify-center"
              >
                <Download size={20} className="text-gray-600" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-6xl mx-auto px-4 pt-20 space-y-6">
        {/* Year Summary Cards - 4x4 Grid Layout */}
        <div className="grid grid-cols-2 gap-3 md:gap-4">
          {/* Top Row */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-2xl p-4 shadow-sm aspect-square flex flex-col justify-between"
          >
            <div className="flex items-center gap-2 mb-2">
              <div className="p-1.5 bg-[#4CAF50]/10 rounded-lg">
                <TrendingUp size={16} className="text-[#4CAF50]" />
              </div>
              <h3 className="text-sm font-semibold text-gray-900">Receitas Totais</h3>
            </div>
            <div className="flex-1 flex flex-col justify-center">
              <p className="text-xl md:text-2xl font-bold text-[#4CAF50] mb-1">
                {new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: 'BRL',
                  notation: 'compact',
                  compactDisplay: 'short'
                }).format(totalIncome)}
              </p>
              <p className="text-xs text-gray-600">
                Média: {new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: 'BRL',
                  notation: 'compact'
                }).format(averageMonthlyIncome)}/mês
              </p>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white rounded-2xl p-4 shadow-sm aspect-square flex flex-col justify-between"
          >
            <div className="flex items-center gap-2 mb-2">
              <div className="p-1.5 bg-[#FF3B30]/10 rounded-lg">
                <TrendingDown size={16} className="text-[#FF3B30]" />
              </div>
              <h3 className="text-sm font-semibold text-gray-900">Despesas Totais</h3>
            </div>
            <div className="flex-1 flex flex-col justify-center">
              <p className="text-xl md:text-2xl font-bold text-[#FF3B30] mb-1">
                {new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: 'BRL',
                  notation: 'compact',
                  compactDisplay: 'short'
                }).format(totalExpenses)}
              </p>
              <p className="text-xs text-gray-600">
                Média: {new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: 'BRL',
                  notation: 'compact'
                }).format(averageMonthlyExpenses)}/mês
              </p>
            </div>
          </motion.div>

          {/* Bottom Row */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white rounded-2xl p-4 shadow-sm aspect-square flex flex-col justify-between"
          >
            <div className="flex items-center gap-2 mb-2">
              <div className="p-1.5 bg-[#B4EB00]/10 rounded-lg">
                <Target size={16} className="text-[#B4EB00]" />
              </div>
              <h3 className="text-sm font-semibold text-gray-900">Economias Totais</h3>
            </div>
            <div className="flex-1 flex flex-col justify-center">
              <p className="text-xl md:text-2xl font-bold text-[#B4EB00] mb-1">
                {new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: 'BRL',
                  notation: 'compact',
                  compactDisplay: 'short'
                }).format(totalSavings)}
              </p>
              <p className="text-xs text-gray-600">
                Taxa: {savingsRate.toFixed(1)}% da receita
              </p>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white rounded-2xl p-4 shadow-sm aspect-square flex flex-col justify-between"
          >
            <div className="flex items-center gap-2 mb-2">
              <div className="p-1.5 bg-[#007AFF]/10 rounded-lg">
                <BarChart3 size={16} className="text-[#007AFF]" />
              </div>
              <h3 className="text-sm font-semibold text-gray-900">Saldo Médio</h3>
            </div>
            <div className="flex-1 flex flex-col justify-center">
              <p className="text-xl md:text-2xl font-bold text-[#007AFF] mb-1">
                {new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: 'BRL',
                  notation: 'compact',
                  compactDisplay: 'short'
                }).format(averageMonthlySavings)}
              </p>
              <p className="text-xs text-gray-600">
                Por mês
              </p>
            </div>
          </motion.div>
        </div>

        {/* Annual Goal Progress */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-2xl p-6 shadow-sm"
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-[#B4EB00]/10 rounded-lg">
                <Target size={20} className="text-[#B4EB00]" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Meta Anual de Economia</h3>
            </div>
            <button
              onClick={() => setIsGoalsModalOpen(true)}
              className="px-4 py-2 bg-[#B4EB00] text-gray-900 rounded-lg hover:bg-opacity-90 transition-colors text-sm font-medium"
            >
              Definir Meta
            </button>
          </div>
          
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Progresso da Meta</span>
              <span className="font-semibold text-gray-900">
                {Math.round(goalProgress)}%
              </span>
            </div>
            
            <div className="w-full bg-gray-100 rounded-full h-4 overflow-hidden">
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: `${Math.min(goalProgress, 100)}%` }}
                transition={{ duration: 1, delay: 0.5 }}
                className="h-full bg-gradient-to-r from-[#B4EB00] to-[#9FD700] rounded-full"
              />
            </div>
            
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">
                Economizado: {new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: 'BRL'
                }).format(totalSavings)}
              </span>
              <span className="text-gray-600">
                Meta: {new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: 'BRL'
                }).format(annualGoal)}
              </span>
            </div>
          </div>
        </motion.div>

        {/* Main Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white rounded-2xl p-6 shadow-sm"
        >
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">
              Evolução Financeira Anual
            </h3>
            <div className="flex gap-2">
              <button
                onClick={() => setViewMode('chart')}
                className={`px-3 py-1 rounded-lg text-sm transition-colors ${
                  viewMode === 'chart'
                    ? 'bg-[#B4EB00] text-gray-900'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                Gráfico
              </button>
              <button
                onClick={() => setViewMode('table')}
                className={`px-3 py-1 rounded-lg text-sm transition-colors ${
                  viewMode === 'table'
                    ? 'bg-[#B4EB00] text-gray-900'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                Tabela
              </button>
            </div>
          </div>

          {viewMode === 'chart' ? (
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={filteredData} margin={{ top: 5, right: 5, bottom: 5, left: 5 }}>
                  <XAxis 
                    dataKey="month" 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: '#6C6C6C' }}
                  />
                  <YAxis 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: '#6C6C6C' }}
                    tickFormatter={(value) => 
                      new Intl.NumberFormat('pt-BR', {
                        notation: 'compact',
                        compactDisplay: 'short'
                      }).format(value)
                    }
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: '#fff',
                      border: 'none',
                      borderRadius: '12px',
                      boxShadow: '0 4px 6px -1px rgba(0,0,0,0.1)',
                      padding: '12px'
                    }}
                    formatter={(value: number, name: string) => [
                      new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                      }).format(value),
                      name === 'income' ? 'Receitas' : 
                      name === 'expenses' ? 'Despesas' : 'Economias'
                    ]}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="income" 
                    stroke="#4CAF50" 
                    strokeWidth={3}
                    dot={{ fill: '#4CAF50', strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="expenses" 
                    stroke="#FF3B30" 
                    strokeWidth={3}
                    dot={{ fill: '#FF3B30', strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="savings" 
                    stroke="#B4EB00" 
                    strokeWidth={3}
                    dot={{ fill: '#B4EB00', strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-100">
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Mês</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-900">Receitas</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-900">Despesas</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-900">Economias</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-900">Saldo</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredData.map((month, index) => (
                    <tr key={month.month} className="border-b border-gray-50 hover:bg-gray-50">
                      <td className="py-3 px-4 font-medium text-gray-900">{month.month}</td>
                      <td className="py-3 px-4 text-right text-[#4CAF50] font-medium">
                        {new Intl.NumberFormat('pt-BR', {
                          style: 'currency',
                          currency: 'BRL'
                        }).format(month.income)}
                      </td>
                      <td className="py-3 px-4 text-right text-[#FF3B30] font-medium">
                        {new Intl.NumberFormat('pt-BR', {
                          style: 'currency',
                          currency: 'BRL'
                        }).format(month.expenses)}
                      </td>
                      <td className="py-3 px-4 text-right text-[#B4EB00] font-medium">
                        {new Intl.NumberFormat('pt-BR', {
                          style: 'currency',
                          currency: 'BRL'
                        }).format(month.savings)}
                      </td>
                      <td className={`py-3 px-4 text-right font-medium ${
                        month.balance >= 0 ? 'text-[#4CAF50]' : 'text-[#FF3B30]'
                      }`}>
                        {new Intl.NumberFormat('pt-BR', {
                          style: 'currency',
                          currency: 'BRL'
                        }).format(month.balance)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* Chart Legend */}
          {viewMode === 'chart' && (
            <div className="flex items-center justify-center gap-6 mt-6 text-sm">
              <div className="flex items-center gap-2">
                <span className="w-3 h-3 rounded-full bg-[#4CAF50]" />
                <span className="text-gray-600">Receitas</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-3 h-3 rounded-full bg-[#FF3B30]" />
                <span className="text-gray-600">Despesas</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-3 h-3 rounded-full bg-[#B4EB00]" />
                <span className="text-gray-600">Economias</span>
              </div>
            </div>
          )}
        </motion.div>

        {/* Category Breakdown and Year Comparison */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Category Breakdown */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="bg-white rounded-2xl p-6 shadow-sm"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-6">
              Principais Categorias de Gastos
            </h3>
            
            <div className="space-y-4">
              {mockCategoryData.map((category, index) => (
                <div key={category.name} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div 
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: category.color }}
                    />
                    <span className="font-medium text-gray-900">{category.name}</span>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-gray-900">
                      {new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                      }).format(category.value)}
                    </p>
                    <p className="text-sm text-gray-500">{category.percentage}%</p>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Year Comparison */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
            className="bg-white rounded-2xl p-6 shadow-sm"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-6">
              Comparativo com Ano Anterior
            </h3>
            
            <div className="space-y-4">
              {mockYearComparison.map((item, index) => (
                <div key={item.category} className="flex items-center justify-between">
                  <span className="font-medium text-gray-900">{item.category}</span>
                  <div className="flex items-center gap-2">
                    <span className={`text-sm font-medium ${
                      item.change >= 0 ? 'text-[#4CAF50]' : 'text-[#FF3B30]'
                    }`}>
                      {item.change >= 0 ? '+' : ''}{item.change.toFixed(1)}%
                    </span>
                    {item.change >= 0 ? (
                      <TrendingUp size={16} className="text-[#4CAF50]" />
                    ) : (
                      <TrendingDown size={16} className="text-[#FF3B30]" />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Highlights Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
          className="bg-white rounded-2xl p-6 shadow-sm"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-6">
            Destaques do Ano
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-[#4CAF50]/5 rounded-xl">
              <h4 className="font-semibold text-[#4CAF50] mb-2">Melhor Mês (Economia)</h4>
              <p className="text-lg font-bold text-gray-900">{bestSavingsMonth.month}</p>
              <p className="text-sm text-gray-600">
                {new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: 'BRL'
                }).format(bestSavingsMonth.savings)}
              </p>
            </div>
            
            <div className="text-center p-4 bg-[#FF3B30]/5 rounded-xl">
              <h4 className="font-semibold text-[#FF3B30] mb-2">Maior Gasto</h4>
              <p className="text-lg font-bold text-gray-900">{highestExpenseMonth.month}</p>
              <p className="text-sm text-gray-600">
                {new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: 'BRL'
                }).format(highestExpenseMonth.expenses)}
              </p>
            </div>
            
            <div className="text-center p-4 bg-[#B4EB00]/5 rounded-xl">
              <h4 className="font-semibold text-[#B4EB00] mb-2">Maior Receita</h4>
              <p className="text-lg font-bold text-gray-900">{highestIncomeMonth.month}</p>
              <p className="text-sm text-gray-600">
                {new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: 'BRL'
                }).format(highestIncomeMonth.income)}
              </p>
            </div>
            
            <div className="text-center p-4 bg-[#007AFF]/5 rounded-xl">
              <h4 className="font-semibold text-[#007AFF] mb-2">Taxa de Economia</h4>
              <p className="text-lg font-bold text-gray-900">{savingsRate.toFixed(1)}%</p>
              <p className="text-sm text-gray-600">da receita total</p>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Filter Modal */}
      <AnimatePresence>
        {isFilterOpen && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[100]"
              onClick={() => setIsFilterOpen(false)}
            />
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-[calc(100%-2rem)] max-w-md bg-white rounded-2xl p-6 shadow-xl z-[101]"
            >
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Filtros e Configurações</h2>
                <button
                  onClick={() => setIsFilterOpen(false)}
                  className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <X size={20} className="text-gray-600" />
                </button>
              </div>

              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Ano
                  </label>
                  <select
                    value={selectedYear}
                    onChange={(e) => setSelectedYear(Number(e.target.value))}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                  >
                    {availableYears.map(year => (
                      <option key={year} value={year}>{year}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Mês (opcional)
                  </label>
                  <select
                    value={selectedMonth}
                    onChange={(e) => setSelectedMonth(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                  >
                    <option value="all">Todos os meses</option>
                    {mockData.map(month => (
                      <option key={month.month} value={month.month}>{month.month}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Categoria (opcional)
                  </label>
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                  >
                    <option value="all">Todas as categorias</option>
                    {mockCategoryData.map(category => (
                      <option key={category.name} value={category.name}>{category.name}</option>
                    ))}
                  </select>
                </div>

                <button
                  onClick={() => setIsFilterOpen(false)}
                  className="w-full px-6 py-3 bg-[#B4EB00] text-gray-900 rounded-lg hover:bg-opacity-90 transition-colors font-medium"
                >
                  Aplicar Filtros
                </button>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>

      {/* Goals Modal */}
      <AnimatePresence>
        {isGoalsModalOpen && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[100]"
              onClick={() => setIsGoalsModalOpen(false)}
            />
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-[calc(100%-2rem)] max-w-md bg-white rounded-2xl p-6 shadow-xl z-[101]"
            >
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Definir Meta Anual</h2>
                <button
                  onClick={() => setIsGoalsModalOpen(false)}
                  className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <X size={20} className="text-gray-600" />
                </button>
              </div>

              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Meta de Economia para {selectedYear}
                  </label>
                  <input
                    type="number"
                    value={annualGoal}
                    onChange={(e) => setAnnualGoal(Number(e.target.value))}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent text-lg"
                    placeholder="25000"
                    min="0"
                    step="100"
                  />
                  <p className="mt-2 text-sm text-gray-600">
                    Valor atual: {new Intl.NumberFormat('pt-BR', {
                      style: 'currency',
                      currency: 'BRL'
                    }).format(annualGoal)}
                  </p>
                </div>

                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2">Sugestões baseadas no histórico:</h4>
                  <div className="space-y-2 text-sm">
                    <p className="text-gray-600">
                      • Meta conservadora: {new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                      }).format(totalSavings * 1.1)} (+10%)
                    </p>
                    <p className="text-gray-600">
                      • Meta moderada: {new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                      }).format(totalSavings * 1.25)} (+25%)
                    </p>
                    <p className="text-gray-600">
                      • Meta ambiciosa: {new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                      }).format(totalSavings * 1.5)} (+50%)
                    </p>
                  </div>
                </div>

                <div className="flex gap-4">
                  <button
                    onClick={() => setIsGoalsModalOpen(false)}
                    className="flex-1 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Cancelar
                  </button>
                  <button
                    onClick={handleSetGoal}
                    className="flex-1 px-4 py-3 bg-[#B4EB00] text-gray-900 rounded-lg hover:bg-opacity-90 transition-colors font-medium"
                  >
                    Definir Meta
                  </button>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default AnnualFinancialOverview;