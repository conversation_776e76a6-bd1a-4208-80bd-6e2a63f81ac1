#!/usr/bin/env node

// Teste para diagnosticar timeout MCP
const SERVER_URL = 'http://localhost:3001/mcp';

console.log('🔍 Diagnosticando timeout MCP...');
console.log(`URL: ${SERVER_URL}`);

// Simular uma requisição MCP initialize
const mcpInitialize = {
  "jsonrpc": "2.0",
  "id": 1,
  "method": "initialize",
  "params": {
    "protocolVersion": "2024-11-05",
    "capabilities": {
      "tools": {}
    },
    "clientInfo": {
      "name": "Test Client",
      "version": "1.0.0"
    }
  }
};

console.log('\n📤 Enviando initialize request...');
console.log('Payload:', JSON.stringify(mcpInitialize, null, 2));

try {
  const response = await fetch(SERVER_URL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'text/event-stream',
      'Cache-Control': 'no-cache'
    },
    body: JSON.stringify(mcpInitialize)
  });

  console.log('\n📥 Response Status:', response.status);
  console.log('Content-Type:', response.headers.get('content-type'));
  console.log('Connection:', response.headers.get('connection'));

  if (!response.ok) {
    console.log('❌ Response not OK');
    const text = await response.text();
    console.log('Response body:', text);
    process.exit(1);
  }

  console.log('\n📊 Reading SSE stream...');
  
  // Configurar timeout
  const timeout = setTimeout(() => {
    console.log('⏰ Timeout after 10 seconds');
    process.exit(1);
  }, 10000);

  // Ler stream
  const reader = response.body.getReader();
  const decoder = new TextDecoder();
  
  let buffer = '';
  let messageCount = 0;
  
  while (true) {
    const { done, value } = await reader.read();
    
    if (done) {
      console.log('📡 Stream ended');
      break;
    }
    
    buffer += decoder.decode(value, { stream: true });
    
    // Processar mensagens SSE
    const lines = buffer.split('\n');
    buffer = lines.pop() || '';
    
    for (const line of lines) {
      if (line.trim()) {
        messageCount++;
        console.log(`📨 Message ${messageCount}:`, line);
        
        // Se recebeu uma mensagem, o servidor está funcionando
        if (line.includes('initialize')) {
          console.log('✅ Received initialize response!');
          clearTimeout(timeout);
          process.exit(0);
        }
      }
    }
  }
  
  clearTimeout(timeout);
  
} catch (error) {
  console.error('❌ Error:', error.message);
  process.exit(1);
}

console.log('\n🎯 Teste concluído');
