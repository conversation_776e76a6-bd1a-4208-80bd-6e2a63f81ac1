import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Request,
  Query,
  ParseIntPipe,
  HttpException,
  HttpStatus,
  Logger,
  Put
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { DevAwareAuthGuard } from '../auth/dev-auth.guard';
import { FinancesService } from './finances.service';
import { CreateFinanceDto } from './dto/create-finance.dto';
import { UpdateFinanceDto } from './dto/update-finance.dto';
import { FinanceResponseDto, FinanceListResponseDto, FinanceSummaryDto } from './dto/finance-response.dto';
import { CreateFinanceCategoryDto } from './dto/create-finance-category.dto';
import { UpdateFinanceCategoryDto } from './dto/update-finance-category.dto';
import { FinanceCategoryResponseDto } from './dto/finance-category-response.dto';

@Controller('finances')
@UseGuards(DevAwareAuthGuard)
export class FinancesController {
  private readonly logger = new Logger(FinancesController.name);

  constructor(private readonly financesService: FinancesService) {}

  @Post()
  async create(@Body() createFinanceDto: CreateFinanceDto, @Request() req): Promise<FinanceResponseDto> {
    try {
      const userId = req.user?.userId;
      const userTimezone = await this.getUserTimezone(userId);

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      return await this.financesService.create(createFinanceDto, userId, userTimezone);
    } catch (error) {
      this.logger.error('Erro ao criar transação financeira:', error);
      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get()
  async findAll(
    @Request() req,
    @Query('page') page = 1,
    @Query('limit') limit = 50
  ): Promise<FinanceListResponseDto> {
    try {
      const userId = req.user?.userId;
      const userTimezone = await this.getUserTimezone(userId);

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      return await this.financesService.findAll(userId, userTimezone, Number(page), Number(limit));
    } catch (error) {
      this.logger.error('Erro ao listar transações financeiras:', error);
      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('summary')
  async getSummary(
    @Request() req,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string
  ): Promise<FinanceSummaryDto> {
    try {
      const userId = req.user?.userId;
      const userTimezone = await this.getUserTimezone(userId);

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      const start = startDate ? new Date(startDate) : undefined;
      const end = endDate ? new Date(endDate) : undefined;

      return await this.financesService.getSummary(userId, userTimezone, start, end);
    } catch (error) {
      this.logger.error('Erro ao buscar resumo financeiro:', error);
      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number, @Request() req): Promise<FinanceResponseDto> {
    try {
      const userId = req.user?.userId;
      const userTimezone = await this.getUserTimezone(userId);

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      return await this.financesService.findOne(id, userId, userTimezone);
    } catch (error) {
      this.logger.error(`Erro ao buscar transação ${id}:`, error);

      if (error.message.includes('não encontrada')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }

      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Put(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateFinanceDto: UpdateFinanceDto,
    @Request() req
  ): Promise<FinanceResponseDto> {
    try {
      const userId = req.user?.userId;
      const userTimezone = await this.getUserTimezone(userId);

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      return await this.financesService.update(id, updateFinanceDto, userId, userTimezone);
    } catch (error) {
      this.logger.error(`Erro ao atualizar transação ${id}:`, error);

      if (error.message.includes('não encontrada')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }

      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Delete(':id')
  async remove(@Param('id', ParseIntPipe) id: number, @Request() req): Promise<{ message: string }> {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      await this.financesService.remove(id, userId);
      return { message: 'Transação financeira removida com sucesso' };
    } catch (error) {
      this.logger.error(`Erro ao remover transação ${id}:`, error);

      if (error.message.includes('não encontrada')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }

      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  private async getUserTimezone(userId: number): Promise<string> {
    // Implementar busca do timezone do usuário
    // Por enquanto, retornar um padrão
    return 'America/Sao_Paulo';
  }

  // Endpoints para categorias financeiras
  @Post('categories')
  async createCategory(@Body() createCategoryDto: CreateFinanceCategoryDto, @Request() req): Promise<FinanceCategoryResponseDto> {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      return await this.financesService.createCategory(createCategoryDto, userId);
    } catch (error) {
      this.logger.error('Erro ao criar categoria financeira:', error);
      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('categories')
  async findAllCategories(@Request() req): Promise<FinanceCategoryResponseDto[]> {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      return await this.financesService.findAllCategories(userId);
    } catch (error) {
      this.logger.error('Erro ao listar categorias financeiras:', error);
      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('categories/:id')
  async findOneCategory(@Param('id', ParseIntPipe) id: number, @Request() req): Promise<FinanceCategoryResponseDto> {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      return await this.financesService.findOneCategory(id, userId);
    } catch (error) {
      this.logger.error(`Erro ao buscar categoria financeira ${id}:`, error);

      if (error.message.includes('não encontrada')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }

      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Put('categories/:id')
  async updateCategory(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateCategoryDto: UpdateFinanceCategoryDto,
    @Request() req
  ): Promise<FinanceCategoryResponseDto> {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      return await this.financesService.updateCategory(id, updateCategoryDto, userId);
    } catch (error) {
      this.logger.error(`Erro ao atualizar categoria financeira ${id}:`, error);

      if (error.message.includes('não encontrada')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }

      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Delete('categories/:id')
  async removeCategory(@Param('id', ParseIntPipe) id: number, @Request() req): Promise<{ message: string }> {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      await this.financesService.removeCategory(id, userId);
      return { message: 'Categoria financeira removida com sucesso' };
    } catch (error) {
      this.logger.error(`Erro ao remover categoria financeira ${id}:`, error);

      if (error.message.includes('não encontrada')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }

      if (error.message.includes('está sendo usada')) {
        throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
      }

      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
