import { CreateIdeaDto } from './dto/create-idea.dto';
import { UpdateIdeaDto } from './dto/update-idea.dto';
import { IdeaResponseDto, IdeaListResponseDto } from './dto/idea-response.dto';
import { CreateIdeaCategoryDto } from './dto/create-idea-category.dto';
import { UpdateIdeaCategoryDto } from './dto/update-idea-category.dto';
import { IdeaCategoryResponseDto } from './dto/idea-category-response.dto';
export declare class IdeasService {
    private readonly logger;
    private db;
    create(createIdeaDto: CreateIdeaDto, userId: number): Promise<IdeaResponseDto>;
    findAll(userId: number, userTimezone: string, page?: number, limit?: number): Promise<IdeaListResponseDto>;
    findOne(id: number, userId: number, userTimezone?: string): Promise<IdeaResponseDto>;
    update(id: number, updateIdeaDto: UpdateIdeaDto, userId: number, userTimezone?: string): Promise<IdeaResponseDto>;
    remove(id: number, userId: number): Promise<void>;
    toggleFavorite(id: number, userId: number, userTimezone?: string): Promise<IdeaResponseDto>;
    createCategory(createCategoryDto: CreateIdeaCategoryDto, userId: number): Promise<IdeaCategoryResponseDto>;
    findAllCategories(userId: number): Promise<IdeaCategoryResponseDto[]>;
    findOneCategory(id: number, userId: number): Promise<IdeaCategoryResponseDto>;
    updateCategory(id: number, updateCategoryDto: UpdateIdeaCategoryDto, userId: number): Promise<IdeaCategoryResponseDto>;
    removeCategory(id: number, userId: number): Promise<void>;
}
