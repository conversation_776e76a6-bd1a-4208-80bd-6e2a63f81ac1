import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { db } from '../database.types';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
import { TaskResponseDto, TaskListResponseDto } from './dto/task-response.dto';
import { CreateTaskCategoryDto } from './dto/create-task-category.dto';
import { UpdateTaskCategoryDto } from './dto/update-task-category.dto';
import { TaskCategoryResponseDto } from './dto/task-category-response.dto';
import { fromUserTimezone, toUserTimezone } from '../utils/timezone';

@Injectable()
export class TasksService {
  private readonly logger = new Logger(TasksService.name);
  private db = db;

  async create(createTaskDto: CreateTaskDto, userId: number, userTimezone: string): Promise<TaskResponseDto> {
    try {
      const taskData = {
        ...createTaskDto,
        user_id: userId,
        task_date: createTaskDto.task_date ? fromUserTimezone(new Date(createTaskDto.task_date), userTimezone) : null,
        created_at: new Date(),
        updated_at: new Date()
      };

      const result = await this.db
        .insertInto('tasks')
        .values(taskData)
        .executeTakeFirst();

      if (!result.insertId) {
        throw new Error('Falha ao criar tarefa');
      }

      this.logger.log(`Tarefa criada com ID ${result.insertId} para usuário ${userId}`);
      return this.findOne(Number(result.insertId), userId, userTimezone);
    } catch (error) {
      this.logger.error(`Erro ao criar tarefa para usuário ${userId}:`, error);
      throw new Error(`Erro ao criar tarefa: ${error.message}`);
    }
  }

  async findAll(userId: number, userTimezone: string, page = 1, limit = 50): Promise<TaskListResponseDto> {
    try {
      const offset = (page - 1) * limit;

      // Buscar tarefas com informações da categoria
      const tasks = await this.db
        .selectFrom('tasks')
        .leftJoin('tasks_categories', 'tasks.category_id', 'tasks_categories.id')
        .select([
          'tasks.id',
          'tasks.task_type',
          'tasks.category_id',
          'tasks_categories.name as category_name',
          'tasks.name',
          'tasks.description',
          'tasks.task_date',
          'tasks.user_id',
          'tasks.completed_at',
          'tasks.created_at',
          'tasks.updated_at'
        ])
        .where('tasks.user_id', '=', userId)
        .orderBy('tasks.created_at', 'desc')
        .limit(limit)
        .offset(offset)
        .execute();

      // Contar total de tarefas
      const totalResult = await this.db
        .selectFrom('tasks')
        .select(this.db.fn.count('id').as('count'))
        .where('user_id', '=', userId)
        .executeTakeFirst();

      const total = Number(totalResult?.count || 0);

      // Converter datas para timezone do usuário
      const tasksWithTimezone = tasks.map(task => ({
        id: task.id,
        task_type: task.task_type,
        category_id: task.category_id || undefined,
        category_name: task.category_name || undefined,
        name: task.name,
        description: task.description || undefined,
        task_date: task.task_date ? toUserTimezone(task.task_date, userTimezone) : undefined,
        user_id: task.user_id,
        completed_at: task.completed_at ? toUserTimezone(task.completed_at, userTimezone) : undefined,
        created_at: toUserTimezone(task.created_at, userTimezone),
        updated_at: toUserTimezone(task.updated_at, userTimezone)
      }));

      this.logger.debug(`Listadas ${tasks.length} tarefas para usuário ${userId}`);

      return {
        tasks: tasksWithTimezone,
        total,
        page,
        limit
      };
    } catch (error) {
      this.logger.error(`Erro ao listar tarefas para usuário ${userId}:`, error);
      throw new Error(`Erro ao listar tarefas: ${error.message}`);
    }
  }

  async findOne(id: number, userId: number, userTimezone: string): Promise<TaskResponseDto> {
    try {
      const task = await this.db
        .selectFrom('tasks')
        .leftJoin('tasks_categories', 'tasks.category_id', 'tasks_categories.id')
        .select([
          'tasks.id',
          'tasks.task_type',
          'tasks.category_id',
          'tasks_categories.name as category_name',
          'tasks.name',
          'tasks.description',
          'tasks.task_date',
          'tasks.user_id',
          'tasks.completed_at',
          'tasks.created_at',
          'tasks.updated_at'
        ])
        .where('tasks.id', '=', id)
        .where('tasks.user_id', '=', userId)
        .executeTakeFirst();

      if (!task) {
        throw new NotFoundException(`Tarefa com ID ${id} não encontrada`);
      }

      return {
        id: task.id,
        task_type: task.task_type,
        category_id: task.category_id || undefined,
        category_name: task.category_name || undefined,
        name: task.name,
        description: task.description || undefined,
        task_date: task.task_date ? toUserTimezone(task.task_date, userTimezone) : undefined,
        user_id: task.user_id,
        completed_at: task.completed_at ? toUserTimezone(task.completed_at, userTimezone) : undefined,
        created_at: toUserTimezone(task.created_at, userTimezone),
        updated_at: toUserTimezone(task.updated_at, userTimezone)
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Erro ao buscar tarefa ${id} para usuário ${userId}:`, error);
      throw new Error(`Erro ao buscar tarefa: ${error.message}`);
    }
  }

  async update(id: number, updateTaskDto: UpdateTaskDto, userId: number, userTimezone: string): Promise<TaskResponseDto> {
    try {
      // Verificar se a tarefa existe e pertence ao usuário
      await this.findOne(id, userId, userTimezone);

      const updateData = {
        ...updateTaskDto,
        task_date: updateTaskDto.task_date ? fromUserTimezone(new Date(updateTaskDto.task_date), userTimezone) : undefined,
        updated_at: new Date()
      };

      // Remover campos undefined
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === undefined) {
          delete updateData[key];
        }
      });

      await this.db
        .updateTable('tasks')
        .set(updateData)
        .where('id', '=', id)
        .where('user_id', '=', userId)
        .execute();

      this.logger.log(`Tarefa ${id} atualizada para usuário ${userId}`);
      return this.findOne(id, userId, userTimezone);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Erro ao atualizar tarefa ${id} para usuário ${userId}:`, error);
      throw new Error(`Erro ao atualizar tarefa: ${error.message}`);
    }
  }

  async remove(id: number, userId: number): Promise<void> {
    try {
      // Verificar se a tarefa existe e pertence ao usuário
      await this.findOne(id, userId, 'UTC'); // Usar UTC para verificação simples

      await this.db
        .deleteFrom('tasks')
        .where('id', '=', id)
        .where('user_id', '=', userId)
        .execute();

      this.logger.log(`Tarefa ${id} removida para usuário ${userId}`);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Erro ao remover tarefa ${id} para usuário ${userId}:`, error);
      throw new Error(`Erro ao remover tarefa: ${error.message}`);
    }
  }

  async complete(id: number, userId: number, userTimezone: string): Promise<TaskResponseDto> {
    try {
      // Verificar se a tarefa existe e pertence ao usuário
      await this.findOne(id, userId, userTimezone);

      await this.db
        .updateTable('tasks')
        .set({
          completed_at: new Date(),
          updated_at: new Date()
        })
        .where('id', '=', id)
        .where('user_id', '=', userId)
        .execute();

      this.logger.log(`Tarefa ${id} marcada como completa para usuário ${userId}`);
      return this.findOne(id, userId, userTimezone);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Erro ao completar tarefa ${id} para usuário ${userId}:`, error);
      throw new Error(`Erro ao completar tarefa: ${error.message}`);
    }
  }

  // Métodos para categorias de tarefas
  async createCategory(createCategoryDto: CreateTaskCategoryDto, userId: number): Promise<TaskCategoryResponseDto> {
    try {
      const categoryData = {
        ...createCategoryDto,
        user_id: userId,
        created_at: new Date(),
        updated_at: new Date()
      };

      const result = await this.db
        .insertInto('tasks_categories')
        .values(categoryData)
        .executeTakeFirst();

      if (!result.insertId) {
        throw new Error('Falha ao criar categoria');
      }

      this.logger.log(`Categoria de tarefa criada com ID ${result.insertId} para usuário ${userId}`);
      return this.findOneCategory(Number(result.insertId), userId);
    } catch (error) {
      this.logger.error(`Erro ao criar categoria para usuário ${userId}:`, error);
      throw new Error(`Erro ao criar categoria: ${error.message}`);
    }
  }

  async findAllCategories(userId: number): Promise<TaskCategoryResponseDto[]> {
    try {
      const categories = await this.db
        .selectFrom('tasks_categories')
        .selectAll()
        .where('user_id', '=', userId)
        .orderBy('name', 'asc')
        .execute();

      this.logger.debug(`Listadas ${categories.length} categorias para usuário ${userId}`);
      return categories;
    } catch (error) {
      this.logger.error(`Erro ao listar categorias para usuário ${userId}:`, error);
      throw new Error(`Erro ao listar categorias: ${error.message}`);
    }
  }

  async findOneCategory(id: number, userId: number): Promise<TaskCategoryResponseDto> {
    try {
      const category = await this.db
        .selectFrom('tasks_categories')
        .selectAll()
        .where('id', '=', id)
        .where('user_id', '=', userId)
        .executeTakeFirst();

      if (!category) {
        throw new NotFoundException(`Categoria com ID ${id} não encontrada`);
      }

      return category;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Erro ao buscar categoria ${id} para usuário ${userId}:`, error);
      throw new Error(`Erro ao buscar categoria: ${error.message}`);
    }
  }

  async updateCategory(id: number, updateCategoryDto: UpdateTaskCategoryDto, userId: number): Promise<TaskCategoryResponseDto> {
    try {
      // Verificar se a categoria existe e pertence ao usuário
      await this.findOneCategory(id, userId);

      const updateData = {
        ...updateCategoryDto,
        updated_at: new Date()
      };

      await this.db
        .updateTable('tasks_categories')
        .set(updateData)
        .where('id', '=', id)
        .where('user_id', '=', userId)
        .execute();

      this.logger.log(`Categoria ${id} atualizada para usuário ${userId}`);
      return this.findOneCategory(id, userId);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Erro ao atualizar categoria ${id} para usuário ${userId}:`, error);
      throw new Error(`Erro ao atualizar categoria: ${error.message}`);
    }
  }

  async removeCategory(id: number, userId: number): Promise<void> {
    try {
      // Verificar se a categoria existe e pertence ao usuário
      await this.findOneCategory(id, userId);

      // Verificar se há tarefas usando esta categoria
      const tasksUsingCategory = await this.db
        .selectFrom('tasks')
        .select(['id'])
        .where('category_id', '=', id)
        .where('user_id', '=', userId)
        .execute();

      if (tasksUsingCategory.length > 0) {
        throw new Error('Não é possível remover categoria que está sendo usada por tarefas');
      }

      await this.db
        .deleteFrom('tasks_categories')
        .where('id', '=', id)
        .where('user_id', '=', userId)
        .execute();

      this.logger.log(`Categoria ${id} removida para usuário ${userId}`);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Erro ao remover categoria ${id} para usuário ${userId}:`, error);
      throw new Error(`Erro ao remover categoria: ${error.message}`);
    }
  }
}
