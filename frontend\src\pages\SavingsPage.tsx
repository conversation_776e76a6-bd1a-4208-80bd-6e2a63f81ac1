import React, { useState } from 'react';
import { ArrowLeft, Search, Filter, ChevronDown, ChevronUp, X, Calendar } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import MonthYearSelector from '../components/MonthYearSelector';

interface Saving {
  id: string;
  description: string;
  amount: number;
  date: string;
  category: string;
}

const mockSavings: Saving[] = [
  {
    id: '1',
    description: 'Reserva Mensal',
    amount: 500.00,
    date: '2024-03-14',
    category: 'Emergência'
  },
  {
    id: '2',
    description: 'Aporte Extra',
    amount: 300.00,
    date: '2024-03-07',
    category: 'Investimentos'
  },
  {
    id: '3',
    description: 'Aporte',
    amount: 450.00,
    date: '2024-03-01',
    category: 'Aposentadoria'
  }
];

const categories = [
  'Emergência',
  'Investimentos',
  'Aposentadoria',
  'Viagem',
  'Outros'
];

const SavingsPage: React.FC = () => {
  const navigate = useNavigate();
  const [savings, setSavings] = useState(mockSavings);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [expandedId, setExpandedId] = useState<string | null>(null);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [filters, setFilters] = useState({
    category: '',
    startDate: '',
    endDate: '',
  });

  const filteredSavings = savings.filter(saving => {
    const matchesSearch = saving.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = !filters.category || saving.category === filters.category;
    const matchesDateRange = (!filters.startDate || saving.date >= filters.startDate) &&
                           (!filters.endDate || saving.date <= filters.endDate);
    
    return matchesSearch && matchesCategory && matchesDateRange;
  });

  const totalSavings = filteredSavings.reduce((sum, saving) => sum + saving.amount, 0);

  const handleDelete = (id: string) => {
    if (confirm('Tem certeza que deseja excluir este aporte?')) {
      setSavings(savings.filter(saving => saving.id !== id));
    }
  };

  const handleSave = (id: string, updatedSaving: Partial<Saving>) => {
    setSavings(savings.map(saving => 
      saving.id === id ? { ...saving, ...updatedSaving } : saving
    ));
    setExpandedId(null);
  };

  const toggleExpand = (id: string) => {
    setExpandedId(expandedId === id ? null : id);
  };

  const clearFilters = () => {
    setFilters({
      category: '',
      startDate: '',
      endDate: '',
    });
  };

  return (
    <div className="max-w-4xl mx-auto min-h-screen p-6 pb-24 md:pb-6">
      <div className="flex items-center justify-between mb-8">
        <button 
          onClick={() => navigate('/finances')}
          className="flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeft size={20} className="mr-1" />
          <span>Voltar</span>
        </button>

        <MonthYearSelector
          selectedDate={selectedDate}
          onDateChange={setSelectedDate}
        />
      </div>

      <div className="bg-white rounded-2xl p-6 shadow-sm mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Economias</h1>
        <p className="text-gray-600 mt-2">
          Total guardado em {new Intl.DateTimeFormat('pt-BR', { month: 'long' }).format(selectedDate)}
        </p>
        <p className="text-3xl font-bold text-[#4CAF50] mt-2">
          {new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL'
          }).format(totalSavings)}
        </p>
      </div>

      <div className="bg-white rounded-2xl p-6 shadow-sm">
        <div className="flex flex-col md:flex-row gap-4 mb-8">
          <div className="flex-1 relative">
            <Search size={20} className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Buscar aporte..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full h-12 pl-12 pr-4 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#4CAF50] focus:border-transparent"
            />
          </div>
          <button 
            onClick={() => setIsFilterOpen(true)}
            className="h-12 px-6 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors flex items-center justify-center gap-2"
          >
            <Filter size={20} className="text-gray-600" />
            <span>Filtros</span>
            {(filters.category || filters.startDate || filters.endDate) && (
              <span className="w-2 h-2 rounded-full bg-[#4CAF50]" />
            )}
          </button>
        </div>

        <div className="space-y-4 max-h-[calc(100vh-24rem)] overflow-y-auto pr-2">
          {filteredSavings.map(saving => (
            <motion.div
              key={saving.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="overflow-hidden border border-gray-100 rounded-xl hover:border-[#4CAF50] transition-colors"
            >
              <div className="p-4 flex items-center justify-between">
                <div className="flex-1">
                  <h3 className="font-medium text-[#1C1C1C]">{saving.description}</h3>
                  <div className="flex items-center gap-4 mt-1">
                    <span className="text-sm text-[#6C6C6C]">
                      {new Date(saving.date).toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' })}
                    </span>
                    <span className="text-sm text-[#6C6C6C]">{saving.category}</span>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <span className="font-medium text-[#4CAF50]">
                    {new Intl.NumberFormat('pt-BR', {
                      style: 'currency',
                      currency: 'BRL'
                    }).format(saving.amount)}
                  </span>
                  <button
                    onClick={() => toggleExpand(saving.id)}
                    className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                  >
                    {expandedId === saving.id ? (
                      <ChevronUp size={20} className="text-gray-600" />
                    ) : (
                      <ChevronDown size={20} className="text-gray-600" />
                    )}
                  </button>
                </div>
              </div>

              <AnimatePresence>
                {expandedId === saving.id && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    className="border-t border-gray-100"
                  >
                    <div className="p-4 space-y-4">
                      <div>
                        <label className="block text-sm text-gray-600 mb-1">Valor</label>
                        <input
                          type="number"
                          value={saving.amount}
                          onChange={(e) => handleSave(saving.id, { amount: parseFloat(e.target.value) })}
                          className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#4CAF50] focus:border-transparent"
                          step="0.01"
                        />
                      </div>

                      <div>
                        <label className="block text-sm text-gray-600 mb-1">Categoria</label>
                        <select
                          value={saving.category}
                          onChange={(e) => handleSave(saving.id, { category: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#4CAF50] focus:border-transparent"
                        >
                          {categories.map(category => (
                            <option key={category} value={category}>
                              {category}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm text-gray-600 mb-1">Data</label>
                        <input
                          type="date"
                          value={saving.date}
                          onChange={(e) => handleSave(saving.id, { date: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#4CAF50] focus:border-transparent"
                        />
                      </div>

                      <div className="flex justify-end gap-2 pt-2">
                        <button
                          onClick={() => handleDelete(saving.id)}
                          className="px-4 py-2 text-[#4CAF50] hover:bg-green-50 rounded-lg transition-colors"
                        >
                          Excluir
                        </button>
                        <button
                          onClick={() => setExpandedId(null)}
                          className="px-4 py-2 bg-[#4CAF50] text-white rounded-lg hover:bg-opacity-90 transition-colors"
                        >
                          Salvar
                        </button>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Filter Modal */}
      <AnimatePresence>
        {isFilterOpen && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[100]"
              onClick={() => setIsFilterOpen(false)}
            />
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-[calc(100%-2rem)] max-w-md bg-white rounded-2xl p-6 shadow-xl z-[101]"
            >
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Filtros</h2>
                <button
                  onClick={() => setIsFilterOpen(false)}
                  className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <X size={20} className="text-gray-600" />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Categoria
                  </label>
                  <select
                    value={filters.category}
                    onChange={(e) => setFilters({ ...filters, category: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4CAF50] focus:border-transparent"
                  >
                    <option value="">Todas as categorias</option>
                    {categories.map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Data inicial
                  </label>
                  <input
                    type="date"
                    value={filters.startDate}
                    onChange={(e) => setFilters({ ...filters, startDate: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4CAF50] focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Data final
                  </label>
                  <input
                    type="date"
                    value={filters.endDate}
                    onChange={(e) => setFilters({ ...filters, endDate: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4CAF50] focus:border-transparent"
                  />
                </div>

                <div className="flex gap-4 pt-4">
                  <button
                    onClick={clearFilters}
                    className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Limpar
                  </button>
                  <button
                    onClick={() => setIsFilterOpen(false)}
                    className="flex-1 px-4 py-2 bg-[#4CAF50] text-white rounded-lg hover:bg-opacity-90 transition-colors"
                  >
                    Aplicar
                  </button>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SavingsPage;