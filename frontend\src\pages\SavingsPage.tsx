import React, { useState } from 'react';
import { ArrowLeft, Search, Filter, ChevronDown, ChevronUp, X, Calendar, PiggyBank, Plus, AlertCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import MonthYearSelector from '../components/MonthYearSelector';
import { useFinances, useCreateFinance, useFinanceCategories } from '../hooks/useFinances';
import { CreateFinanceDto } from '../types/api';

interface Saving {
  id: string;
  description: string;
  amount: number;
  date: string;
  category: string;
}

const mockSavings: Saving[] = [
  {
    id: '1',
    description: 'Reserva Mensal',
    amount: 500.00,
    date: '2024-03-14',
    category: 'Emergência'
  },
  {
    id: '2',
    description: 'Aporte Extra',
    amount: 300.00,
    date: '2024-03-07',
    category: 'Investimentos'
  },
  {
    id: '3',
    description: 'Aporte',
    amount: 450.00,
    date: '2024-03-01',
    category: 'Aposentadoria'
  }
];

const categories = [
  'Emergência',
  'Investimentos',
  'Aposentadoria',
  'Viagem',
  'Outros'
];

const SavingsPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [expandedId, setExpandedId] = useState<number | null>(null);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [filters, setFilters] = useState({
    category: '',
    startDate: '',
    endDate: '',
  });
  const [newSaving, setNewSaving] = useState({
    description: '',
    amount: '',
    category_id: undefined as number | undefined
  });

  // Fetch data from API
  const { data: financesData, isLoading, error } = useFinances();
  const { data: categoriesData } = useFinanceCategories();
  const createFinanceMutation = useCreateFinance();

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando economias...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertCircle className="h-8 w-8 text-red-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Erro ao carregar dados</h2>
          <p className="text-gray-600 mb-4">
            Não foi possível carregar suas economias. Tente novamente.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }

  // Filter savings (transactions marked as savings)
  const savings = financesData?.finances.filter(finance => finance.is_saving) || [];
  const savingsCategories = categoriesData?.filter(cat => cat.transaction_type === 'expense') || [];

  const filteredSavings = savings.filter(saving => {
    const matchesSearch = (saving.description || '').toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = !filters.category || saving.category_name === filters.category;
    const savingDate = new Date(saving.transaction_date).toISOString().split('T')[0];
    const matchesDateRange = (!filters.startDate || savingDate >= filters.startDate) &&
                           (!filters.endDate || savingDate <= filters.endDate);

    return matchesSearch && matchesCategory && matchesDateRange;
  });

  const totalSavings = filteredSavings.reduce((sum, saving) => sum + parseFloat(saving.amount), 0);

  const handleAddSaving = async () => {
    if (!newSaving.description.trim() || !newSaving.amount) return;

    try {
      const savingData: CreateFinanceDto = {
        transaction_type: 'expense',
        description: newSaving.description,
        amount: newSaving.amount,
        category_id: newSaving.category_id,
        is_saving: true,
        transaction_date: new Date().toISOString()
      };

      await createFinanceMutation.mutateAsync(savingData);
      setNewSaving({ description: '', amount: '', category_id: undefined });
      setIsAddModalOpen(false);
    } catch (error) {
      console.error('Erro ao adicionar economia:', error);
    }
  };

  const toggleExpand = (id: number) => {
    setExpandedId(expandedId === id ? null : id);
  };

  const clearFilters = () => {
    setFilters({
      category: '',
      startDate: '',
      endDate: '',
    });
  };

  return (
    <div className="max-w-4xl mx-auto min-h-screen p-6 pb-24 md:pb-6">
      <div className="flex items-center justify-between mb-8">
        <button 
          onClick={() => navigate('/finances')}
          className="flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeft size={20} className="mr-1" />
          <span>Voltar</span>
        </button>

        <MonthYearSelector
          selectedDate={selectedDate}
          onDateChange={setSelectedDate}
        />
      </div>

      <div className="bg-white rounded-2xl p-6 shadow-sm mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Economias</h1>
            <p className="text-gray-600 mt-2">
              Total guardado em {new Intl.DateTimeFormat('pt-BR', { month: 'long' }).format(selectedDate)}
            </p>
          </div>
          <button
            onClick={() => setIsAddModalOpen(true)}
            className="flex items-center gap-2 px-4 py-2 bg-[#4CAF50] text-white rounded-lg hover:bg-opacity-90 transition-colors"
          >
            <Plus size={20} />
            Adicionar
          </button>
        </div>
        <p className="text-3xl font-bold text-[#4CAF50]">
          {new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL'
          }).format(totalSavings)}
        </p>
      </div>

      <div className="bg-white rounded-2xl p-6 shadow-sm">
        <div className="flex flex-col md:flex-row gap-4 mb-8">
          <div className="flex-1 relative">
            <Search size={20} className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Buscar aporte..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full h-12 pl-12 pr-4 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#4CAF50] focus:border-transparent"
            />
          </div>
          <button 
            onClick={() => setIsFilterOpen(true)}
            className="h-12 px-6 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors flex items-center justify-center gap-2"
          >
            <Filter size={20} className="text-gray-600" />
            <span>Filtros</span>
            {(filters.category || filters.startDate || filters.endDate) && (
              <span className="w-2 h-2 rounded-full bg-[#4CAF50]" />
            )}
          </button>
        </div>

        <div className="space-y-4 max-h-[calc(100vh-24rem)] overflow-y-auto pr-2">
          {filteredSavings.map(saving => (
            <motion.div
              key={saving.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="overflow-hidden border border-gray-100 rounded-xl hover:border-[#4CAF50] transition-colors"
            >
              <div className="p-4 flex items-center justify-between">
                <div className="flex-1">
                  <h3 className="font-medium text-[#1C1C1C]">{saving.description || 'Economia'}</h3>
                  <div className="flex items-center gap-4 mt-1">
                    <span className="text-sm text-[#6C6C6C]">
                      {new Date(saving.transaction_date).toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' })}
                    </span>
                    {saving.category_name && (
                      <span className="text-sm text-[#6C6C6C]">{saving.category_name}</span>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <span className="font-medium text-[#4CAF50]">
                    {new Intl.NumberFormat('pt-BR', {
                      style: 'currency',
                      currency: 'BRL'
                    }).format(parseFloat(saving.amount))}
                  </span>
                  <button
                    onClick={() => toggleExpand(saving.id)}
                    className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                  >
                    {expandedId === saving.id ? (
                      <ChevronUp size={20} className="text-gray-600" />
                    ) : (
                      <ChevronDown size={20} className="text-gray-600" />
                    )}
                  </button>
                </div>
              </div>

              <AnimatePresence>
                {expandedId === saving.id && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    className="border-t border-gray-100"
                  >
                    <div className="p-4 space-y-4">
                      <div>
                        <label className="block text-sm text-gray-600 mb-1">Valor</label>
                        <input
                          type="number"
                          value={saving.amount}
                          onChange={(e) => handleSave(saving.id, { amount: parseFloat(e.target.value) })}
                          className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#4CAF50] focus:border-transparent"
                          step="0.01"
                        />
                      </div>

                      <div>
                        <label className="block text-sm text-gray-600 mb-1">Categoria</label>
                        <select
                          value={saving.category}
                          onChange={(e) => handleSave(saving.id, { category: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#4CAF50] focus:border-transparent"
                        >
                          {categories.map(category => (
                            <option key={category} value={category}>
                              {category}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm text-gray-600 mb-1">Data</label>
                        <input
                          type="date"
                          value={saving.date}
                          onChange={(e) => handleSave(saving.id, { date: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#4CAF50] focus:border-transparent"
                        />
                      </div>

                      <div className="flex justify-end gap-2 pt-2">
                        <button
                          onClick={() => handleDelete(saving.id)}
                          className="px-4 py-2 text-[#4CAF50] hover:bg-green-50 rounded-lg transition-colors"
                        >
                          Excluir
                        </button>
                        <button
                          onClick={() => setExpandedId(null)}
                          className="px-4 py-2 bg-[#4CAF50] text-white rounded-lg hover:bg-opacity-90 transition-colors"
                        >
                          Salvar
                        </button>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Filter Modal */}
      <AnimatePresence>
        {isFilterOpen && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[100]"
              onClick={() => setIsFilterOpen(false)}
            />
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-[calc(100%-2rem)] max-w-md bg-white rounded-2xl p-6 shadow-xl z-[101]"
            >
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Filtros</h2>
                <button
                  onClick={() => setIsFilterOpen(false)}
                  className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <X size={20} className="text-gray-600" />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Categoria
                  </label>
                  <select
                    value={filters.category}
                    onChange={(e) => setFilters({ ...filters, category: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4CAF50] focus:border-transparent"
                  >
                    <option value="">Todas as categorias</option>
                    {savingsCategories.map(category => (
                      <option key={category.id} value={category.name}>{category.name}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Data inicial
                  </label>
                  <input
                    type="date"
                    value={filters.startDate}
                    onChange={(e) => setFilters({ ...filters, startDate: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4CAF50] focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Data final
                  </label>
                  <input
                    type="date"
                    value={filters.endDate}
                    onChange={(e) => setFilters({ ...filters, endDate: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4CAF50] focus:border-transparent"
                  />
                </div>

                <div className="flex gap-4 pt-4">
                  <button
                    onClick={clearFilters}
                    className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Limpar
                  </button>
                  <button
                    onClick={() => setIsFilterOpen(false)}
                    className="flex-1 px-4 py-2 bg-[#4CAF50] text-white rounded-lg hover:bg-opacity-90 transition-colors"
                  >
                    Aplicar
                  </button>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>

      {/* Add Saving Modal */}
      <AnimatePresence>
        {isAddModalOpen && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[100]"
              onClick={() => setIsAddModalOpen(false)}
            />
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-[calc(100%-2rem)] max-w-md bg-white rounded-2xl p-6 shadow-xl z-[101]"
            >
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Nova Economia</h2>
                <button
                  onClick={() => setIsAddModalOpen(false)}
                  className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <X size={20} className="text-gray-600" />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Descrição
                  </label>
                  <input
                    type="text"
                    value={newSaving.description}
                    onChange={(e) => setNewSaving({ ...newSaving, description: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4CAF50] focus:border-transparent"
                    placeholder="Ex: Reserva de emergência"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Valor
                  </label>
                  <input
                    type="number"
                    value={newSaving.amount}
                    onChange={(e) => setNewSaving({ ...newSaving, amount: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4CAF50] focus:border-transparent"
                    placeholder="0,00"
                    step="0.01"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Categoria
                  </label>
                  <select
                    value={newSaving.category_id || ''}
                    onChange={(e) => setNewSaving({ ...newSaving, category_id: e.target.value ? parseInt(e.target.value) : undefined })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#4CAF50] focus:border-transparent"
                  >
                    <option value="">Selecione uma categoria</option>
                    {savingsCategories.map(category => (
                      <option key={category.id} value={category.id}>{category.name}</option>
                    ))}
                  </select>
                </div>

                <div className="flex gap-4 pt-4">
                  <button
                    onClick={() => setIsAddModalOpen(false)}
                    className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Cancelar
                  </button>
                  <button
                    onClick={handleAddSaving}
                    disabled={createFinanceMutation.isPending || !newSaving.description.trim() || !newSaving.amount}
                    className="flex-1 px-4 py-2 bg-[#4CAF50] text-white rounded-lg hover:bg-opacity-90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {createFinanceMutation.isPending ? 'Salvando...' : 'Salvar'}
                  </button>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SavingsPage;