import { useQuery } from '@tanstack/react-query';
import { authenticatedApi } from '../lib/api';
import { DashboardResponseDto } from '../types/api';

export const useDashboardData = () => {
  return useQuery({
    queryKey: ['dashboard'],
    queryFn: async (): Promise<DashboardResponseDto> => {
      const response = await authenticatedApi.get('dashboard');
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
    retry: (failureCount, error: any) => {
      // Não tentar novamente para erros de autenticação
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        return false;
      }
      return failureCount < 2;
    },
  });
};
