import React, { useState } from 'react';
import { User, <PERSON>ting<PERSON>, CreditCard, Share2, Upload, Check, ExternalLink } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';

interface ProfileData {
  name: string;
  email: string;
  phone: string;
  photo?: string;
}

interface CustomizeSettings {
  aiMood: 'formal' | 'moderate' | 'casual';
  responseLength: 'short' | 'medium' | 'long';
  reminderTime: string;
  reminderDelay: '15' | '30' | '60' | '180' | '360' | '1440';
}

interface PlanData {
  type: 'monthly' | 'annual';
  status: 'active';
  nextBilling: string;
  paymentMethod: string;
  aiCredits: number;
}

interface Integration {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  status: 'connected' | 'disconnected' | 'coming_soon';
  connectedDetails?: string;
}

const ProfilePage: React.FC = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('profile');
  const [profileData, setProfileData] = useState<ProfileData>({
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '(11) 98765-4321'
  });

  const [customizeSettings, setCustomizeSettings] = useState<CustomizeSettings>({
    aiMood: 'moderate',
    responseLength: 'medium',
    reminderTime: '09:00',
    reminderDelay: '30'
  });

  const [planData] = useState<PlanData>({
    type: 'monthly',
    status: 'active',
    nextBilling: '2025-06-25',
    paymentMethod: 'Cartão final 1234',
    aiCredits: 1500
  });

  const [integrations] = useState<Integration[]>([
    {
      id: 'whatsapp',
      name: 'WhatsApp',
      description: 'Principal canal de comunicação',
      icon: <div className="w-6 h-6 text-[#25D366]" />,
      status: 'connected',
      connectedDetails: '+55 11 98765-4321'
    },
    {
      id: 'google-calendar',
      name: 'Google Agenda',
      description: 'Sincronize seus compromissos',
      icon: <div className="w-6 h-6 text-[#4285F4]" />,
      status: 'disconnected'
    },
    {
      id: 'google-sheets',
      name: 'Google Sheets',
      description: 'Exporte seus dados',
      icon: <div className="w-6 h-6 text-[#0F9D58]" />,
      status: 'disconnected'
    }
  ]);

  const tabs = [
    { id: 'profile', icon: User, label: 'Perfil' },
    { id: 'customize', icon: Settings, label: 'Assistant' },
    { id: 'plan', icon: CreditCard, label: 'Plano' },
    { id: 'integrations', icon: Share2, label: 'Integrações' }
  ];

  return (
    <div className="min-h-screen bg-[#F7F7F7] pb-24">
      {/* Profile Card */}
      <div className="bg-white">
        <div className="max-w-2xl mx-auto px-4 py-6">
          <div className="flex items-center gap-4">
            <div className="relative">
              <div className="w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center overflow-hidden">
                {profileData.photo ? (
                  <img src={profileData.photo} alt={profileData.name} className="w-full h-full object-cover" />
                ) : (
                  <span className="text-2xl font-semibold text-gray-900">{profileData.name[0]}</span>
                )}
              </div>
              <button className="absolute bottom-0 right-0 p-1 bg-[#B4EB00] rounded-full">
                <Upload size={14} className="text-gray-900" />
              </button>
            </div>
            <div className="flex-1 min-w-0">
              <h1 className="text-xl font-semibold text-gray-900 truncate">{profileData.name}</h1>
              <p className="text-gray-600 truncate">{profileData.email}</p>
              <div className="inline-flex items-center gap-1 px-2 py-1 bg-[#B4EB00]/10 rounded-full mt-2">
                <Check size={14} className="text-[#B4EB00]" />
                <span className="text-xs font-medium text-gray-900">Premium</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="bg-white border-t border-b border-gray-100">
        <div className="max-w-2xl mx-auto px-4">
          <div className="flex justify-between">
            {tabs.map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex flex-col items-center py-3 px-4 transition-colors relative ${
                  activeTab === tab.id ? 'text-[#B4EB00]' : 'text-gray-600'
                }`}
              >
                <tab.icon size={20} />
                <span className="text-xs mt-1">{tab.label}</span>
                {activeTab === tab.id && (
                  <motion.div
                    layoutId="activeTab"
                    className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#B4EB00]"
                  />
                )}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-2xl mx-auto px-4 py-6">
        <AnimatePresence mode="wait">
          {activeTab === 'profile' && (
            <motion.div
              key="profile"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              <div className="bg-white rounded-2xl p-6 shadow-sm">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">Informações Pessoais</h2>
                <form className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Nome</label>
                    <input
                      type="text"
                      value={profileData.name}
                      onChange={(e) => setProfileData({ ...profileData, name: e.target.value })}
                      className="w-full h-12 px-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                    <input
                      type="email"
                      value={profileData.email}
                      onChange={(e) => setProfileData({ ...profileData, email: e.target.value })}
                      className="w-full h-12 px-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Telefone</label>
                    <input
                      type="tel"
                      value={profileData.phone}
                      onChange={(e) => setProfileData({ ...profileData, phone: e.target.value })}
                      className="w-full h-12 px-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                    />
                  </div>
                  <button
                    type="submit"
                    className="w-full h-12 bg-[#B4EB00] text-gray-900 rounded-xl font-medium hover:bg-opacity-90 transition-colors"
                  >
                    Salvar Alterações
                  </button>
                </form>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-sm">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">Segurança</h2>
                <button
                  className="w-full h-12 border border-gray-200 rounded-xl text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Alterar Senha
                </button>
              </div>
            </motion.div>
          )}

          {activeTab === 'customize' && (
            <motion.div
              key="customize"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              <div className="bg-white rounded-2xl p-6 shadow-sm">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-lg font-semibold text-gray-900">Personalizar Assistente</h2>
                  <span className="px-2 py-1 bg-[#B4EB00]/10 rounded-full text-xs font-medium text-gray-900">Beta</span>
                </div>
                <form className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Humor da IA</label>
                    <select
                      value={customizeSettings.aiMood}
                      onChange={(e) => setCustomizeSettings({ ...customizeSettings, aiMood: e.target.value as any })}
                      className="w-full h-12 px-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                    >
                      <option value="formal">Formal</option>
                      <option value="moderate">Moderado</option>
                      <option value="casual">Descontraído</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Tamanho das Respostas</label>
                    <select
                      value={customizeSettings.responseLength}
                      onChange={(e) => setCustomizeSettings({ ...customizeSettings, responseLength: e.target.value as any })}
                      className="w-full h-12 px-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                    >
                      <option value="short">Curtas</option>
                      <option value="medium">Médias</option>
                      <option value="long">Longas</option>
                    </select>
                  </div>
                  <button
                    type="submit"
                    className="w-full h-12 bg-[#B4EB00] text-gray-900 rounded-xl font-medium hover:bg-opacity-90 transition-colors"
                  >
                    Salvar Preferências
                  </button>
                </form>
              </div>
            </motion.div>
          )}

          {activeTab === 'plan' && (
            <motion.div
              key="plan"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              <div className="bg-white rounded-2xl p-6 shadow-sm">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">Plano Atual</h2>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Plano</span>
                    <span className="font-medium text-gray-900">Premium Mensal</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Próxima cobrança</span>
                    <span className="font-medium text-gray-900">25/06/2025</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Método de pagamento</span>
                    <span className="font-medium text-gray-900">{planData.paymentMethod}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Créditos de IA</span>
                    <span className="font-medium text-gray-900">{planData.aiCredits}</span>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-sm">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">Comprar Créditos</h2>
                <button
                  className="w-full h-12 bg-[#B4EB00] text-gray-900 rounded-xl font-medium hover:bg-opacity-90 transition-colors"
                >
                  Adicionar Créditos
                </button>
              </div>
            </motion.div>
          )}

          {activeTab === 'integrations' && (
            <motion.div
              key="integrations"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-4"
            >
              {integrations.map(integration => (
                <div
                  key={integration.id}
                  className="bg-white rounded-2xl p-6 shadow-sm"
                >
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 rounded-xl bg-gray-100 flex items-center justify-center">
                      {integration.icon}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-semibold text-gray-900">{integration.name}</h3>
                        {integration.status === 'connected' && (
                          <span className="px-2 py-1 bg-[#B4EB00]/10 rounded-full text-xs font-medium text-gray-900">
                            Conectado
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mb-4">{integration.description}</p>
                      {integration.connectedDetails && (
                        <p className="text-sm text-gray-500 mb-4">{integration.connectedDetails}</p>
                      )}
                      <button
                        className={`inline-flex items-center gap-2 h-10 px-4 rounded-xl transition-colors ${
                          integration.status === 'connected'
                            ? 'text-gray-700 hover:bg-gray-100'
                            : 'bg-[#B4EB00] text-gray-900 hover:bg-opacity-90'
                        }`}
                      >
                        <span>
                          {integration.status === 'connected' ? 'Gerenciar' : 'Conectar'}
                        </span>
                        <ExternalLink size={16} />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default ProfilePage;