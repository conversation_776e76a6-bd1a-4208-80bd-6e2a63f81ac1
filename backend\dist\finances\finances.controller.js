"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var FinancesController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.FinancesController = void 0;
const common_1 = require("@nestjs/common");
const dev_auth_guard_1 = require("../auth/dev-auth.guard");
const finances_service_1 = require("./finances.service");
const create_finance_dto_1 = require("./dto/create-finance.dto");
const update_finance_dto_1 = require("./dto/update-finance.dto");
const create_finance_category_dto_1 = require("./dto/create-finance-category.dto");
const update_finance_category_dto_1 = require("./dto/update-finance-category.dto");
let FinancesController = FinancesController_1 = class FinancesController {
    financesService;
    logger = new common_1.Logger(FinancesController_1.name);
    constructor(financesService) {
        this.financesService = financesService;
    }
    async create(createFinanceDto, req) {
        try {
            const userId = req.user?.userId;
            const userTimezone = await this.getUserTimezone(userId);
            if (!userId) {
                throw new common_1.HttpException('ID do usuário não encontrado', common_1.HttpStatus.UNAUTHORIZED);
            }
            return await this.financesService.create(createFinanceDto, userId, userTimezone);
        }
        catch (error) {
            this.logger.error('Erro ao criar transação financeira:', error);
            throw new common_1.HttpException(error.message || 'Erro interno do servidor', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async findAll(req, page = 1, limit = 50) {
        try {
            const userId = req.user?.userId;
            const userTimezone = await this.getUserTimezone(userId);
            if (!userId) {
                throw new common_1.HttpException('ID do usuário não encontrado', common_1.HttpStatus.UNAUTHORIZED);
            }
            return await this.financesService.findAll(userId, userTimezone, Number(page), Number(limit));
        }
        catch (error) {
            this.logger.error('Erro ao listar transações financeiras:', error);
            throw new common_1.HttpException(error.message || 'Erro interno do servidor', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getSummary(req, startDate, endDate) {
        try {
            const userId = req.user?.userId;
            const userTimezone = await this.getUserTimezone(userId);
            if (!userId) {
                throw new common_1.HttpException('ID do usuário não encontrado', common_1.HttpStatus.UNAUTHORIZED);
            }
            const start = startDate ? new Date(startDate) : undefined;
            const end = endDate ? new Date(endDate) : undefined;
            return await this.financesService.getSummary(userId, userTimezone, start, end);
        }
        catch (error) {
            this.logger.error('Erro ao buscar resumo financeiro:', error);
            throw new common_1.HttpException(error.message || 'Erro interno do servidor', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async findOne(id, req) {
        try {
            const userId = req.user?.userId;
            const userTimezone = await this.getUserTimezone(userId);
            if (!userId) {
                throw new common_1.HttpException('ID do usuário não encontrado', common_1.HttpStatus.UNAUTHORIZED);
            }
            return await this.financesService.findOne(id, userId, userTimezone);
        }
        catch (error) {
            this.logger.error(`Erro ao buscar transação ${id}:`, error);
            if (error.message.includes('não encontrada')) {
                throw new common_1.HttpException(error.message, common_1.HttpStatus.NOT_FOUND);
            }
            throw new common_1.HttpException(error.message || 'Erro interno do servidor', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async update(id, updateFinanceDto, req) {
        try {
            const userId = req.user?.userId;
            const userTimezone = await this.getUserTimezone(userId);
            if (!userId) {
                throw new common_1.HttpException('ID do usuário não encontrado', common_1.HttpStatus.UNAUTHORIZED);
            }
            return await this.financesService.update(id, updateFinanceDto, userId, userTimezone);
        }
        catch (error) {
            this.logger.error(`Erro ao atualizar transação ${id}:`, error);
            if (error.message.includes('não encontrada')) {
                throw new common_1.HttpException(error.message, common_1.HttpStatus.NOT_FOUND);
            }
            throw new common_1.HttpException(error.message || 'Erro interno do servidor', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async remove(id, req) {
        try {
            const userId = req.user?.userId;
            if (!userId) {
                throw new common_1.HttpException('ID do usuário não encontrado', common_1.HttpStatus.UNAUTHORIZED);
            }
            await this.financesService.remove(id, userId);
            return { message: 'Transação financeira removida com sucesso' };
        }
        catch (error) {
            this.logger.error(`Erro ao remover transação ${id}:`, error);
            if (error.message.includes('não encontrada')) {
                throw new common_1.HttpException(error.message, common_1.HttpStatus.NOT_FOUND);
            }
            throw new common_1.HttpException(error.message || 'Erro interno do servidor', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getUserTimezone(userId) {
        return 'America/Sao_Paulo';
    }
    async createCategory(createCategoryDto, req) {
        try {
            const userId = req.user?.userId;
            if (!userId) {
                throw new common_1.HttpException('ID do usuário não encontrado', common_1.HttpStatus.UNAUTHORIZED);
            }
            return await this.financesService.createCategory(createCategoryDto, userId);
        }
        catch (error) {
            this.logger.error('Erro ao criar categoria financeira:', error);
            throw new common_1.HttpException(error.message || 'Erro interno do servidor', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async findAllCategories(req) {
        try {
            const userId = req.user?.userId;
            if (!userId) {
                throw new common_1.HttpException('ID do usuário não encontrado', common_1.HttpStatus.UNAUTHORIZED);
            }
            return await this.financesService.findAllCategories(userId);
        }
        catch (error) {
            this.logger.error('Erro ao listar categorias financeiras:', error);
            throw new common_1.HttpException(error.message || 'Erro interno do servidor', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async findOneCategory(id, req) {
        try {
            const userId = req.user?.userId;
            if (!userId) {
                throw new common_1.HttpException('ID do usuário não encontrado', common_1.HttpStatus.UNAUTHORIZED);
            }
            return await this.financesService.findOneCategory(id, userId);
        }
        catch (error) {
            this.logger.error(`Erro ao buscar categoria financeira ${id}:`, error);
            if (error.message.includes('não encontrada')) {
                throw new common_1.HttpException(error.message, common_1.HttpStatus.NOT_FOUND);
            }
            throw new common_1.HttpException(error.message || 'Erro interno do servidor', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updateCategory(id, updateCategoryDto, req) {
        try {
            const userId = req.user?.userId;
            if (!userId) {
                throw new common_1.HttpException('ID do usuário não encontrado', common_1.HttpStatus.UNAUTHORIZED);
            }
            return await this.financesService.updateCategory(id, updateCategoryDto, userId);
        }
        catch (error) {
            this.logger.error(`Erro ao atualizar categoria financeira ${id}:`, error);
            if (error.message.includes('não encontrada')) {
                throw new common_1.HttpException(error.message, common_1.HttpStatus.NOT_FOUND);
            }
            throw new common_1.HttpException(error.message || 'Erro interno do servidor', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async removeCategory(id, req) {
        try {
            const userId = req.user?.userId;
            if (!userId) {
                throw new common_1.HttpException('ID do usuário não encontrado', common_1.HttpStatus.UNAUTHORIZED);
            }
            await this.financesService.removeCategory(id, userId);
            return { message: 'Categoria financeira removida com sucesso' };
        }
        catch (error) {
            this.logger.error(`Erro ao remover categoria financeira ${id}:`, error);
            if (error.message.includes('não encontrada')) {
                throw new common_1.HttpException(error.message, common_1.HttpStatus.NOT_FOUND);
            }
            if (error.message.includes('está sendo usada')) {
                throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
            }
            throw new common_1.HttpException(error.message || 'Erro interno do servidor', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.FinancesController = FinancesController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_finance_dto_1.CreateFinanceDto, Object]),
    __metadata("design:returntype", Promise)
], FinancesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", Promise)
], FinancesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('summary'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)('startDate')),
    __param(2, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], FinancesController.prototype, "getSummary", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], FinancesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_finance_dto_1.UpdateFinanceDto, Object]),
    __metadata("design:returntype", Promise)
], FinancesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], FinancesController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)('categories'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_finance_category_dto_1.CreateFinanceCategoryDto, Object]),
    __metadata("design:returntype", Promise)
], FinancesController.prototype, "createCategory", null);
__decorate([
    (0, common_1.Get)('categories'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], FinancesController.prototype, "findAllCategories", null);
__decorate([
    (0, common_1.Get)('categories/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], FinancesController.prototype, "findOneCategory", null);
__decorate([
    (0, common_1.Put)('categories/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_finance_category_dto_1.UpdateFinanceCategoryDto, Object]),
    __metadata("design:returntype", Promise)
], FinancesController.prototype, "updateCategory", null);
__decorate([
    (0, common_1.Delete)('categories/:id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], FinancesController.prototype, "removeCategory", null);
exports.FinancesController = FinancesController = FinancesController_1 = __decorate([
    (0, common_1.Controller)('finances'),
    (0, common_1.UseGuards)(dev_auth_guard_1.DevAwareAuthGuard),
    __metadata("design:paramtypes", [finances_service_1.FinancesService])
], FinancesController);
//# sourceMappingURL=finances.controller.js.map