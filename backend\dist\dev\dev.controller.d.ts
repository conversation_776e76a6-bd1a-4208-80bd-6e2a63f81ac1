import { JwtService } from '@nestjs/jwt';
import { DevService } from './dev.service';
export declare class DevController {
    private readonly jwtService;
    private readonly devService;
    constructor(jwtService: JwtService, devService: DevService);
    generateDevToken(body: {
        userId?: number;
        email?: string;
    }): Promise<{
        access_token: string;
        user: {
            id: number;
            email: string;
            name: string;
            timezone: string;
        };
        message: string;
    }>;
    getOrCreateTestUser(): Promise<{
        password: string | null | undefined;
        timezone: string;
        name: string;
        email: string;
        id: number;
        phone: string | null | undefined;
        created_at: Date;
        updated_at: Date;
        deleted_at: Date | null | undefined;
    }>;
    seedTestData(body: {
        userId?: number;
    }): Promise<{
        message: string;
    }>;
}
