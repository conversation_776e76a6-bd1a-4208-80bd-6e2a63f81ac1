import { Injectable } from '@nestjs/common';
import { Kysely } from 'kysely';
import { Database } from '../database.types';
import { InjectKysely } from 'nestjs-kysely';

@Injectable()
export class DevService {
  constructor(@InjectKysely() private readonly db: Kysely<Database>) {}

  async getOrCreateTestUser() {
    // Verificar se usuário de teste já existe
    let user = await this.db
      .selectFrom('users')
      .selectAll()
      .where('email', '=', '<EMAIL>')
      .executeTakeFirst();

    if (!user) {
      // Criar usuário de teste
      const result = await this.db
        .insertInto('users')
        .values({
          name: 'Dev User',
          email: '<EMAIL>',
          password_hash: 'dev-password-hash', // Senha não importa para testes
          phone: '+5511999999999',
          timezone: 'America/Sao_Paulo',
          created_at: new Date(),
          updated_at: new Date()
        })
        .executeTakeFirst();

      user = await this.db
        .selectFrom('users')
        .selectAll()
        .where('id', '=', Number(result.insertId))
        .executeTakeFirstOrThrow();
    }

    return user;
  }

  async seedTestData(userId: number) {
    // Criar categorias de teste se não existirem
    const existingCategories = await this.db
      .selectFrom('finance_categories')
      .selectAll()
      .where('user_id', '=', userId)
      .execute();

    if (existingCategories.length === 0) {
      const categories = [
        { name: 'Alimentação', transaction_type: 'expense' as const, color: '#B4EB00' },
        { name: 'Transporte', transaction_type: 'expense' as const, color: '#212121' },
        { name: 'Moradia', transaction_type: 'expense' as const, color: '#6C6C6C' },
        { name: 'Lazer', transaction_type: 'expense' as const, color: '#BBBBBB' },
        { name: 'Saúde', transaction_type: 'expense' as const, color: '#E5E7EB' },
        { name: 'Outros', transaction_type: 'expense' as const, color: '#9CA3AF' },
        { name: 'Salário', transaction_type: 'income' as const, color: '#4CAF50' },
        { name: 'Freelance', transaction_type: 'income' as const, color: '#2196F3' }
      ];

      for (const category of categories) {
        await this.db
          .insertInto('finance_categories')
          .values({
            ...category,
            user_id: userId,
            created_at: new Date(),
            updated_at: new Date()
          })
          .execute();
      }
    }

    // Criar algumas transações de teste se não existirem
    const existingTransactions = await this.db
      .selectFrom('finances')
      .selectAll()
      .where('user_id', '=', userId)
      .execute();

    if (existingTransactions.length === 0) {
      const categories = await this.db
        .selectFrom('finance_categories')
        .selectAll()
        .where('user_id', '=', userId)
        .execute();

      const transactions = [
        { amount: '5000.00', description: 'Salário', transaction_type: 'income' as const, category_id: categories.find(c => c.name === 'Salário')?.id },
        { amount: '800.00', description: 'Supermercado', transaction_type: 'expense' as const, category_id: categories.find(c => c.name === 'Alimentação')?.id },
        { amount: '300.00', description: 'Uber', transaction_type: 'expense' as const, category_id: categories.find(c => c.name === 'Transporte')?.id },
        { amount: '1200.00', description: 'Aluguel', transaction_type: 'expense' as const, category_id: categories.find(c => c.name === 'Moradia')?.id },
        { amount: '500.00', description: 'Poupança', transaction_type: 'expense' as const, category_id: categories.find(c => c.name === 'Outros')?.id, is_saving: true }
      ];

      for (const transaction of transactions) {
        if (transaction.category_id) {
          await this.db
            .insertInto('finances')
            .values({
              ...transaction,
              user_id: userId,
              transaction_date: new Date(),
              created_at: new Date(),
              updated_at: new Date()
            })
            .execute();
        }
      }
    }

    return { message: 'Dados de teste criados com sucesso' };
  }
}
