import { Controller, Post, Get, Body, HttpException, HttpStatus } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { DevService } from './dev.service';

@Controller('dev')
export class DevController {
  constructor(
    private readonly jwtService: JwtService,
    private readonly devService: DevService
  ) {}

  @Post('generate-token')
  async generateDevToken(@Body() body: { userId?: number; email?: string }) {
    // Só permitir em modo de desenvolvimento
    if (process.env.NODE_ENV !== 'development' || process.env.DEV_MODE_ENABLED !== 'true') {
      throw new HttpException('Endpoint disponível apenas em modo de desenvolvimento', HttpStatus.FORBIDDEN);
    }

    const userId = body.userId || 1;
    const email = body.email || '<EMAIL>';

    // Gerar token JWT
    const payload = { sub: userId, email };
    const access_token = this.jwtService.sign(payload);

    return {
      access_token,
      user: {
        id: userId,
        email,
        name: 'Dev User',
        timezone: 'America/Sao_Paulo'
      },
      message: 'Token de desenvolvimento gerado com sucesso'
    };
  }

  @Get('test-user')
  async getOrCreateTestUser() {
    // Só permitir em modo de desenvolvimento
    if (process.env.NODE_ENV !== 'development' || process.env.DEV_MODE_ENABLED !== 'true') {
      throw new HttpException('Endpoint disponível apenas em modo de desenvolvimento', HttpStatus.FORBIDDEN);
    }

    return await this.devService.getOrCreateTestUser();
  }

  @Post('seed-data')
  async seedTestData(@Body() body: { userId?: number }) {
    // Só permitir em modo de desenvolvimento
    if (process.env.NODE_ENV !== 'development' || process.env.DEV_MODE_ENABLED !== 'true') {
      throw new HttpException('Endpoint disponível apenas em modo de desenvolvimento', HttpStatus.FORBIDDEN);
    }

    const userId = body.userId || 1;
    return await this.devService.seedTestData(userId);
  }
}
