import React, { useState } from 'react';
import { Search, Plus, Heart, MoreVertical, X, Check, AlertCircle } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import Header from '../components/Header';
import IdeaNotepad from '../components/IdeaNotepad';
import { useIdeas, useCreateIdea, useUpdateIdea, useDeleteIdea, useToggleFavoriteIdea, useIdeaCategories } from '../hooks/useIdeas';
import { IdeaResponseDto, CreateIdeaDto, UpdateIdeaDto } from '../types/api';

interface NewIdea {
  name: string;
  description: string;
  category_id?: number;
}

const IdeasPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [showFavorites, setShowFavorites] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [expandedId, setExpandedId] = useState<number | null>(null);
  const [editMode, setEditMode] = useState<number | null>(null);
  const [newIdea, setNewIdea] = useState<NewIdea>({
    name: '',
    description: '',
    category_id: undefined
  });
  const [editingIdea, setEditingIdea] = useState<IdeaResponseDto | null>(null);
  const [selectedIdea, setSelectedIdea] = useState<IdeaResponseDto | null>(null);

  // Fetch data from API
  const { data: ideasData, isLoading: ideasLoading, error: ideasError } = useIdeas();
  const { data: categoriesData } = useIdeaCategories();
  const createIdeaMutation = useCreateIdea();
  const updateIdeaMutation = useUpdateIdea();
  const deleteIdeaMutation = useDeleteIdea();
  const toggleFavoriteMutation = useToggleFavoriteIdea();

  // Loading state
  if (ideasLoading) {
    return (
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando ideias...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (ideasError) {
    return (
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertCircle className="h-8 w-8 text-red-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Erro ao carregar ideias</h2>
          <p className="text-gray-600 mb-4">
            Não foi possível carregar suas ideias. Tente novamente.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }

  const ideas = ideasData?.data || [];
  const categories = categoriesData || [];

  const filteredIdeas = ideas.filter(idea => {
    const matchesSearch = (
      idea.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (idea.description && idea.description.toLowerCase().includes(searchTerm.toLowerCase()))
    );
    const matchesFavorites = showFavorites ? idea.is_favorite : true;
    return matchesSearch && matchesFavorites;
  });

  const groupedIdeas = filteredIdeas.reduce((groups, idea) => {
    const date = new Date(idea.created_at);
    const dateKey = date.toISOString().split('T')[0];

    if (!groups[dateKey]) {
      groups[dateKey] = [];
    }
    groups[dateKey].push(idea);
    return groups;
  }, {} as Record<string, IdeaResponseDto[]>);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (date.toDateString() === today.toDateString()) {
      return 'Hoje';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Ontem';
    } else {
      return new Intl.DateTimeFormat('pt-BR', {
        weekday: 'long',
        day: 'numeric',
        month: 'long'
      }).format(date);
    }
  };

  const todayIdeas = ideas.filter(idea =>
    new Date(idea.created_at).toDateString() === new Date().toDateString()
  ).length;

  const toggleFavorite = async (id: number, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await toggleFavoriteMutation.mutateAsync(id);
    } catch (error) {
      console.error('Erro ao alterar favorito:', error);
    }
  };

  const handleAddIdea = async () => {
    if (!newIdea.name.trim()) return;

    try {
      const ideaData: CreateIdeaDto = {
        name: newIdea.name,
        description: newIdea.description,
        category_id: newIdea.category_id,
        is_favorite: false
      };

      await createIdeaMutation.mutateAsync(ideaData);
      setNewIdea({ name: '', description: '', category_id: undefined });
      setIsModalOpen(false);
    } catch (error) {
      console.error('Erro ao criar ideia:', error);
    }
  };

  const handleUpdateIdea = async (id: number) => {
    if (!editingIdea) return;

    try {
      const updateData: UpdateIdeaDto = {
        name: editingIdea.name,
        description: editingIdea.description,
        category_id: editingIdea.category_id
      };

      await updateIdeaMutation.mutateAsync({ id, data: updateData });
      setEditMode(null);
      setEditingIdea(null);
    } catch (error) {
      console.error('Erro ao atualizar ideia:', error);
    }
  };

  const handleDeleteIdea = async (id: number, e: React.MouseEvent) => {
    e.stopPropagation();
    if (confirm('Tem certeza que deseja excluir esta ideia?')) {
      try {
        await deleteIdeaMutation.mutateAsync(id);
      } catch (error) {
        console.error('Erro ao deletar ideia:', error);
      }
    }
  };

  const handleIdeaClick = (idea: IdeaResponseDto) => {
    setSelectedIdea(idea);
  };

  const handleNotepadClose = () => {
    setSelectedIdea(null);
  };

  const handleNotepadSave = async (id: number, updates: { title: string; content: string }) => {
    try {
      const updateData: UpdateIdeaDto = {
        name: updates.title,
        content: updates.content,
        description: updates.content.slice(0, 100) + (updates.content.length > 100 ? '...' : '')
      };

      await updateIdeaMutation.mutateAsync({ id, data: updateData });
    } catch (error) {
      console.error('Erro ao salvar ideia:', error);
    }
  };

  const handleMoreOptions = (idea: IdeaResponseDto, e: React.MouseEvent) => {
    e.stopPropagation();
    setEditMode(idea.id);
    setEditingIdea(idea);
    setExpandedId(expandedId === idea.id ? null : idea.id);
  };

  return (
    <>
      <div className="min-h-screen bg-[#F7F7F7] pb-24 md:pb-6">
        <Header onAddClick={() => setIsModalOpen(true)} />

        <div className="max-w-7xl mx-auto px-4 md:px-6">
          <div className="pt-20">
            {/* Search and filters */}
            <div className="grid grid-cols-1 md:grid-cols-[1fr,auto] gap-4 mb-6">
              <div className="relative">
                <Search 
                  size={20} 
                  className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" 
                />
                <input
                  type="text"
                  placeholder="Buscar ideias..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full h-12 pl-12 pr-4 bg-white rounded-xl shadow-sm focus:ring-2 focus:ring-black focus:border-transparent"
                />
              </div>

              <div className="flex gap-3">
                <button
                  onClick={() => setShowFavorites(!showFavorites)}
                  className={`px-6 py-2 rounded-full transition-colors ${
                    showFavorites
                      ? 'bg-black text-white'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  Favoritos
                </button>
                <button
                  onClick={() => setIsModalOpen(true)}
                  className="px-6 py-2 bg-black text-white rounded-full hover:bg-opacity-90 transition-colors flex items-center gap-2"
                >
                  <Plus size={20} />
                  <span className="hidden md:inline">Adicionar</span>
                </button>
              </div>
            </div>

            {/* Summary card */}
            <div className="bg-white rounded-xl p-6 shadow-sm mb-6">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-xl font-semibold text-gray-900">Suas ideias</h1>
                  <p className="text-gray-600">{ideas.length} ideias no total</p>
                </div>
                {todayIdeas > 0 && (
                  <span className="text-sm text-gray-600">{todayIdeas} hoje</span>
                )}
              </div>
            </div>

            {/* Ideas grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Object.entries(groupedIdeas).map(([date, dateIdeas]) => (
                <div key={date} className="space-y-4">
                  <h2 className="text-sm text-gray-600 mb-3 pl-1">
                    {formatDate(date)}
                  </h2>
                  <div className="space-y-4">
                    {dateIdeas.map(idea => (
                      <motion.div
                        key={idea.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        whileHover={{ y: -2 }}
                        className="bg-white rounded-xl shadow-sm overflow-hidden cursor-pointer hover:shadow-md transition-all"
                        onClick={() => handleIdeaClick(idea)}
                      >
                        <div className="p-4">
                          <div className="flex items-start justify-between">
                            <div className="flex-1 min-w-0">
                              <h3 className="font-medium text-gray-900 line-clamp-2 mb-2">
                                {idea.name}
                              </h3>
                              <p className="text-gray-600 text-sm line-clamp-3 mb-3">
                                {idea.description}
                              </p>
                              <div className="flex items-center gap-2">
                                <span className="text-xs text-gray-500">
                                  {new Date(idea.created_at).toLocaleDateString('pt-BR')}
                                </span>
                                {idea.category_name && (
                                  <span className="px-2 py-1 bg-gray-100 rounded-full text-xs text-gray-600">
                                    {idea.category_name}
                                  </span>
                                )}
                              </div>
                            </div>
                            <div className="flex items-start gap-1 ml-2">
                              <button
                                onClick={(e) => toggleFavorite(idea.id, e)}
                                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                              >
                                <Heart
                                  size={18}
                                  className={idea.is_favorite ? 'fill-red-500 text-red-500' : 'text-gray-400'}
                                />
                              </button>
                              <button
                                onClick={(e) => handleMoreOptions(idea, e)}
                                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                              >
                                <MoreVertical size={18} className="text-gray-400" />
                              </button>
                            </div>
                          </div>
                        </div>

                        <AnimatePresence>
                          {expandedId === idea.id && (
                            <motion.div
                              initial={{ height: 0 }}
                              animate={{ height: 'auto' }}
                              exit={{ height: 0 }}
                              className="border-t border-gray-100"
                            >
                              <div className="p-4 space-y-4">
                                <div>
                                  <label className="block text-sm text-gray-600 mb-1">
                                    Título
                                  </label>
                                  <input
                                    type="text"
                                    value={editingIdea?.name ?? idea.name}
                                    onChange={(e) => setEditingIdea(prev => prev ? { ...prev, name: e.target.value } : null)}
                                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                                  />
                                </div>

                                <div>
                                  <label className="block text-sm text-gray-600 mb-1">
                                    Descrição
                                  </label>
                                  <textarea
                                    value={editingIdea?.description ?? idea.description}
                                    onChange={(e) => setEditingIdea(prev => prev ? { ...prev, description: e.target.value } : null)}
                                    rows={3}
                                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent resize-none"
                                  />
                                </div>

                                <div>
                                  <label className="block text-sm text-gray-600 mb-1">
                                    Categoria
                                  </label>
                                  <select
                                    value={editingIdea?.category_id ?? idea.category_id ?? ''}
                                    onChange={(e) => setEditingIdea(prev => prev ? { ...prev, category_id: e.target.value ? parseInt(e.target.value) : undefined } : null)}
                                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                                  >
                                    <option value="">Selecione uma categoria</option>
                                    {categories.map(category => (
                                      <option key={category.id} value={category.id}>
                                        {category.name}
                                      </option>
                                    ))}
                                  </select>
                                </div>

                                <div className="flex justify-between pt-2">
                                  <button
                                    onClick={(e) => handleDeleteIdea(idea.id, e)}
                                    className="px-4 py-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                                  >
                                    Excluir
                                  </button>
                                  <div className="flex gap-2">
                                    <button
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        setExpandedId(null);
                                        setEditingIdea(null);
                                      }}
                                      className="px-4 py-2 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                                    >
                                      Cancelar
                                    </button>
                                    <button
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleUpdateIdea(idea.id);
                                      }}
                                      className="px-4 py-2 bg-black text-white rounded-lg hover:bg-opacity-90 transition-colors"
                                    >
                                      Salvar
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </motion.div>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            {/* Empty state */}
            {filteredIdeas.length === 0 && (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Plus size={24} className="text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {searchTerm ? 'Nenhuma ideia encontrada' : 'Suas primeiras ideias'}
                </h3>
                <p className="text-gray-500 text-sm mb-4">
                  {searchTerm 
                    ? 'Tente ajustar sua busca ou criar uma nova ideia'
                    : 'Comece capturando suas ideias e inspirações aqui'
                  }
                </p>
                <button
                  onClick={() => setIsModalOpen(true)}
                  className="inline-flex items-center gap-2 px-6 py-3 bg-black text-white rounded-xl hover:bg-opacity-90 transition-colors"
                >
                  <Plus size={20} />
                  Adicionar primeira ideia
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Add Idea Modal */}
        <AnimatePresence>
          {isModalOpen && (
            <>
              {/* Backdrop Overlay */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[1000]"
                onClick={() => setIsModalOpen(false)}
              />
              
              {/* Modal Container */}
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{ duration: 0.2, ease: "easeOut" }}
                className="fixed inset-0 z-[1001] flex items-center justify-center p-4"
              >
                <div className="w-full max-w-md max-h-[90vh] bg-white rounded-2xl shadow-xl overflow-hidden flex flex-col">
                  {/* Header */}
                  <div className="flex-shrink-0 flex justify-between items-center p-6 border-b border-gray-100">
                    <h2 className="text-xl font-semibold text-gray-900">
                      Adicionar nova ideia
                    </h2>
                    <button
                      onClick={() => setIsModalOpen(false)}
                      className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                    >
                      <X size={20} className="text-gray-600" />
                    </button>
                  </div>

                  {/* Content */}
                  <div className="flex-1 overflow-y-auto p-6 space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Título
                      </label>
                      <input
                        type="text"
                        value={newIdea.name}
                        onChange={(e) => setNewIdea({ ...newIdea, name: e.target.value })}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                        placeholder="Digite o título da sua ideia"
                        autoFocus
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Descrição (opcional)
                      </label>
                      <textarea
                        value={newIdea.description}
                        onChange={(e) => setNewIdea({ ...newIdea, description: e.target.value })}
                        rows={4}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent resize-none"
                        placeholder="Descreva sua ideia..."
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Categoria
                      </label>
                      <select
                        value={newIdea.category_id || ''}
                        onChange={(e) => setNewIdea({ ...newIdea, category_id: e.target.value ? parseInt(e.target.value) : undefined })}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                      >
                        <option value="">Selecione uma categoria</option>
                        {categories.map(category => (
                          <option key={category.id} value={category.id}>
                            {category.name}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* Footer */}
                  <div className="flex-shrink-0 flex gap-4 p-6 border-t border-gray-100">
                    <button
                      onClick={() => setIsModalOpen(false)}
                      className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                    >
                      Cancelar
                    </button>
                    <button
                      onClick={handleAddIdea}
                      disabled={!newIdea.name.trim()}
                      className="flex-1 px-4 py-2 bg-black text-white rounded-lg hover:bg-opacity-90 transition-colors flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <Check size={20} />
                      Adicionar
                    </button>
                  </div>
                </div>
              </motion.div>
            </>
          )}
        </AnimatePresence>
      </div>

      {/* Notepad Interface */}
      <AnimatePresence>
        {selectedIdea && (
          <IdeaNotepad
            id={selectedIdea.id}
            title={selectedIdea.name}
            content={selectedIdea.content || selectedIdea.description || ''}
            onClose={handleNotepadClose}
            onSave={handleNotepadSave}
          />
        )}
      </AnimatePresence>
    </>
  );
};

export default IdeasPage;