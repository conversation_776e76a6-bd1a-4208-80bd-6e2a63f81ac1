import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { DevController } from './dev.controller';
import { DevService } from './dev.service';

@Module({
  imports: [
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'secret',
      signOptions: { expiresIn: process.env.JWT_EXPIRES_IN || '15m' },
    }),
  ],
  controllers: [DevController],
  providers: [DevService],
})
export class DevModule {}
