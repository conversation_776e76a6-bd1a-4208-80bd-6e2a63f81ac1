import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { db } from '../database.types';
import { CreateIdeaDto } from './dto/create-idea.dto';
import { UpdateIdeaDto } from './dto/update-idea.dto';
import { IdeaResponseDto, IdeaListResponseDto } from './dto/idea-response.dto';
import { CreateIdeaCategoryDto } from './dto/create-idea-category.dto';
import { UpdateIdeaCategoryDto } from './dto/update-idea-category.dto';
import { IdeaCategoryResponseDto } from './dto/idea-category-response.dto';
import { toUserTimezone } from '../utils/timezone';

@Injectable()
export class IdeasService {
  private readonly logger = new Logger(IdeasService.name);
  private db = db;

  async create(createIdeaDto: CreateIdeaDto, userId: number): Promise<IdeaResponseDto> {
    try {
      const ideaData = {
        ...createIdeaDto,
        user_id: userId,
        created_at: new Date(),
        updated_at: new Date()
      };

      const result = await this.db
        .insertInto('ideas')
        .values(ideaData)
        .executeTakeFirst();

      if (!result.insertId) {
        throw new Error('Falha ao criar ideia');
      }

      this.logger.log(`Ideia criada com ID ${result.insertId} para usuário ${userId}`);
      return this.findOne(Number(result.insertId), userId);
    } catch (error) {
      this.logger.error(`Erro ao criar ideia para usuário ${userId}:`, error);
      throw new Error(`Erro ao criar ideia: ${error.message}`);
    }
  }

  async findAll(userId: number, userTimezone: string, page = 1, limit = 50): Promise<IdeaListResponseDto> {
    try {
      const offset = (page - 1) * limit;

      // Buscar ideias com informações da categoria
      const ideas = await this.db
        .selectFrom('ideas')
        .leftJoin('ideas_categories', 'ideas.category_id', 'ideas_categories.id')
        .select([
          'ideas.id',
          'ideas.category_id',
          'ideas_categories.name as category_name',
          'ideas.name',
          'ideas.description',
          'ideas.content',
          'ideas.is_favorite',
          'ideas.user_id',
          'ideas.created_at',
          'ideas.updated_at'
        ])
        .where('ideas.user_id', '=', userId)
        .orderBy('ideas.created_at', 'desc')
        .limit(limit)
        .offset(offset)
        .execute();

      // Contar total de ideias
      const totalResult = await this.db
        .selectFrom('ideas')
        .select(this.db.fn.count('id').as('count'))
        .where('user_id', '=', userId)
        .executeTakeFirst();

      const total = Number(totalResult?.count || 0);

      // Converter datas para timezone do usuário
      const ideasWithTimezone = ideas.map(idea => ({
        id: idea.id,
        category_id: idea.category_id || undefined,
        category_name: idea.category_name || undefined,
        name: idea.name,
        description: idea.description || undefined,
        content: idea.content || undefined,
        is_favorite: idea.is_favorite || undefined,
        user_id: idea.user_id,
        created_at: toUserTimezone(idea.created_at, userTimezone),
        updated_at: toUserTimezone(idea.updated_at, userTimezone)
      }));

      this.logger.debug(`Listadas ${ideas.length} ideias para usuário ${userId}`);

      return {
        ideas: ideasWithTimezone,
        total,
        page,
        limit
      };
    } catch (error) {
      this.logger.error(`Erro ao listar ideias para usuário ${userId}:`, error);
      throw new Error(`Erro ao listar ideias: ${error.message}`);
    }
  }

  async findOne(id: number, userId: number, userTimezone = 'America/Sao_Paulo'): Promise<IdeaResponseDto> {
    try {
      const idea = await this.db
        .selectFrom('ideas')
        .leftJoin('ideas_categories', 'ideas.category_id', 'ideas_categories.id')
        .select([
          'ideas.id',
          'ideas.category_id',
          'ideas_categories.name as category_name',
          'ideas.name',
          'ideas.description',
          'ideas.content',
          'ideas.is_favorite',
          'ideas.user_id',
          'ideas.created_at',
          'ideas.updated_at'
        ])
        .where('ideas.id', '=', id)
        .where('ideas.user_id', '=', userId)
        .executeTakeFirst();

      if (!idea) {
        throw new NotFoundException(`Ideia com ID ${id} não encontrada`);
      }

      return {
        id: idea.id,
        category_id: idea.category_id || undefined,
        category_name: idea.category_name || undefined,
        name: idea.name,
        description: idea.description || undefined,
        content: idea.content || undefined,
        is_favorite: idea.is_favorite || undefined,
        user_id: idea.user_id,
        created_at: toUserTimezone(idea.created_at, userTimezone),
        updated_at: toUserTimezone(idea.updated_at, userTimezone)
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Erro ao buscar ideia ${id} para usuário ${userId}:`, error);
      throw new Error(`Erro ao buscar ideia: ${error.message}`);
    }
  }

  async update(id: number, updateIdeaDto: UpdateIdeaDto, userId: number, userTimezone = 'America/Sao_Paulo'): Promise<IdeaResponseDto> {
    try {
      // Verificar se a ideia existe e pertence ao usuário
      await this.findOne(id, userId, userTimezone);

      const updateData = {
        ...updateIdeaDto,
        updated_at: new Date()
      };

      // Remover campos undefined
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === undefined) {
          delete updateData[key];
        }
      });

      await this.db
        .updateTable('ideas')
        .set(updateData)
        .where('id', '=', id)
        .where('user_id', '=', userId)
        .execute();

      this.logger.log(`Ideia ${id} atualizada para usuário ${userId}`);
      return this.findOne(id, userId, userTimezone);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Erro ao atualizar ideia ${id} para usuário ${userId}:`, error);
      throw new Error(`Erro ao atualizar ideia: ${error.message}`);
    }
  }

  async remove(id: number, userId: number): Promise<void> {
    try {
      // Verificar se a ideia existe e pertence ao usuário
      await this.findOne(id, userId);

      await this.db
        .deleteFrom('ideas')
        .where('id', '=', id)
        .where('user_id', '=', userId)
        .execute();

      this.logger.log(`Ideia ${id} removida para usuário ${userId}`);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Erro ao remover ideia ${id} para usuário ${userId}:`, error);
      throw new Error(`Erro ao remover ideia: ${error.message}`);
    }
  }

  async toggleFavorite(id: number, userId: number, userTimezone = 'America/Sao_Paulo'): Promise<IdeaResponseDto> {
    try {
      // Verificar se a ideia existe e pertence ao usuário
      const idea = await this.findOne(id, userId, userTimezone);

      const newFavoriteStatus = !idea.is_favorite;

      await this.db
        .updateTable('ideas')
        .set({
          is_favorite: newFavoriteStatus,
          updated_at: new Date()
        })
        .where('id', '=', id)
        .where('user_id', '=', userId)
        .execute();

      this.logger.log(`Ideia ${id} ${newFavoriteStatus ? 'marcada como favorita' : 'desmarcada como favorita'} para usuário ${userId}`);
      return this.findOne(id, userId, userTimezone);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Erro ao alterar status de favorito da ideia ${id} para usuário ${userId}:`, error);
      throw new Error(`Erro ao alterar status de favorito: ${error.message}`);
    }
  }

  // Métodos para categorias de ideias
  async createCategory(createCategoryDto: CreateIdeaCategoryDto, userId: number): Promise<IdeaCategoryResponseDto> {
    try {
      const categoryData = {
        ...createCategoryDto,
        user_id: userId,
        created_at: new Date(),
        updated_at: new Date()
      };

      const result = await this.db
        .insertInto('ideas_categories')
        .values(categoryData)
        .executeTakeFirst();

      if (!result.insertId) {
        throw new Error('Falha ao criar categoria de ideia');
      }

      this.logger.log(`Categoria de ideia criada com ID ${result.insertId} para usuário ${userId}`);
      return this.findOneCategory(Number(result.insertId), userId);
    } catch (error) {
      this.logger.error(`Erro ao criar categoria de ideia para usuário ${userId}:`, error);
      throw new Error(`Erro ao criar categoria de ideia: ${error.message}`);
    }
  }

  async findAllCategories(userId: number): Promise<IdeaCategoryResponseDto[]> {
    try {
      const categories = await this.db
        .selectFrom('ideas_categories')
        .selectAll()
        .where('user_id', '=', userId)
        .orderBy('name', 'asc')
        .execute();

      this.logger.debug(`Listadas ${categories.length} categorias de ideias para usuário ${userId}`);
      return categories;
    } catch (error) {
      this.logger.error(`Erro ao listar categorias de ideias para usuário ${userId}:`, error);
      throw new Error(`Erro ao listar categorias de ideias: ${error.message}`);
    }
  }

  async findOneCategory(id: number, userId: number): Promise<IdeaCategoryResponseDto> {
    try {
      const category = await this.db
        .selectFrom('ideas_categories')
        .selectAll()
        .where('id', '=', id)
        .where('user_id', '=', userId)
        .executeTakeFirst();

      if (!category) {
        throw new NotFoundException(`Categoria de ideia com ID ${id} não encontrada`);
      }

      return category;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Erro ao buscar categoria de ideia ${id} para usuário ${userId}:`, error);
      throw new Error(`Erro ao buscar categoria de ideia: ${error.message}`);
    }
  }

  async updateCategory(id: number, updateCategoryDto: UpdateIdeaCategoryDto, userId: number): Promise<IdeaCategoryResponseDto> {
    try {
      // Verificar se a categoria existe e pertence ao usuário
      await this.findOneCategory(id, userId);

      const updateData = {
        ...updateCategoryDto,
        updated_at: new Date()
      };

      await this.db
        .updateTable('ideas_categories')
        .set(updateData)
        .where('id', '=', id)
        .where('user_id', '=', userId)
        .execute();

      this.logger.log(`Categoria de ideia ${id} atualizada para usuário ${userId}`);
      return this.findOneCategory(id, userId);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Erro ao atualizar categoria de ideia ${id} para usuário ${userId}:`, error);
      throw new Error(`Erro ao atualizar categoria de ideia: ${error.message}`);
    }
  }

  async removeCategory(id: number, userId: number): Promise<void> {
    try {
      // Verificar se a categoria existe e pertence ao usuário
      await this.findOneCategory(id, userId);

      // Verificar se há ideias usando esta categoria
      const ideasUsingCategory = await this.db
        .selectFrom('ideas')
        .select(['id'])
        .where('category_id', '=', id)
        .where('user_id', '=', userId)
        .execute();

      if (ideasUsingCategory.length > 0) {
        throw new Error('Não é possível remover categoria que está sendo usada por ideias');
      }

      await this.db
        .deleteFrom('ideas_categories')
        .where('id', '=', id)
        .where('user_id', '=', userId)
        .execute();

      this.logger.log(`Categoria de ideia ${id} removida para usuário ${userId}`);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Erro ao remover categoria de ideia ${id} para usuário ${userId}:`, error);
      throw new Error(`Erro ao remover categoria de ideia: ${error.message}`);
    }
  }
}
