import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Star, Trash2, Share2 } from 'lucide-react';
import { motion } from 'framer-motion';
import { formatDate } from '@/lib/utils';

interface Idea {
  id: string;
  title: string;
  description: string;
  content?: string;
  date: string;
  isFavorite: boolean;
  category: string;
}

const IdeaDetailPage: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [idea, setIdea] = useState<Idea | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulating API call
    const mockIdea: Idea = {
      id: id!,
      title: 'App para Freelancers',
      description: 'Desenvolver uma plataforma que ajude freelancers a gerenciar projetos, prazos e pagamentos de forma simples e intuitiva.',
      content: 'Desenvolver uma plataforma que ajude freelancers a gerenciar projetos, prazos e pagamentos de forma simples e intuitiva. Incluir recursos de tracking de tempo e geração automática de relatórios.',
      date: '2024-03-15',
      isFavorite: true,
      category: 'App'
    };

    setTimeout(() => {
      setIdea(mockIdea);
      setIsLoading(false);
    }, 500);
  }, [id]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-2 border-[#B4EB00] border-t-transparent" />
      </div>
    );
  }

  if (!idea) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-xl font-semibold text-gray-900 mb-2">Ideia não encontrada</h1>
          <button
            onClick={() => navigate('/ideas')}
            className="text-[#B4EB00] hover:underline"
          >
            Voltar para lista de ideias
          </button>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="min-h-screen bg-white"
    >
      {/* Header */}
      <div className="fixed top-0 left-0 right-0 bg-white border-b border-gray-100 z-10">
        <div className="max-w-2xl mx-auto px-4">
          <div className="h-16 flex items-center justify-between">
            <button
              onClick={() => navigate('/ideas')}
              className="w-10 h-10 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
            >
              <ArrowLeft size={20} className="text-gray-600" />
            </button>
            <div className="flex items-center gap-2">
              <button className="w-10 h-10 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors">
                <Share2 size={20} className="text-gray-600" />
              </button>
              <button className="w-10 h-10 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors">
                <Star
                  size={20}
                  className={idea.isFavorite ? 'fill-[#F9D449] text-[#F9D449]' : 'text-gray-400'}
                />
              </button>
              <button className="w-10 h-10 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors">
                <Trash2 size={20} className="text-red-500" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-2xl mx-auto px-4 pt-24 pb-16">
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{idea.title}</h1>
            <div className="flex items-center gap-3 mt-3">
              <span className="text-sm text-gray-500">
                {formatDate(idea.date)}
              </span>
              <span className="px-2 py-1 bg-gray-100 rounded-full text-sm text-gray-600">
                {idea.category}
              </span>
            </div>
          </div>

          <div className="prose prose-lg">
            <p className="text-gray-600 whitespace-pre-wrap">
              {idea.content || idea.description}
            </p>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default IdeaDetailPage;